package config

import (
	"encoding/json"
	"fmt"
	"io"
	"math"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
)

var (
	globalConfig *Config
	configMutex  sync.RWMutex
	ConfigPath   string
)

type ApiOptimization struct {
	MaxSamples           int     `json:"max_samples"`
	MinSimilarity        float64 `json:"min_similarity"`
	TokenLimit           int     `json:"token_limit"`
	SpecStandardization  bool    `json:"spec_standardization"`
	UnifiedTagThreshold  float64 `json:"unified_tag_threshold"`
	UnifiedSpecThreshold float64 `json:"unified_spec_threshold"`
	UnifiedNameThreshold float64 `json:"unified_name_threshold"`
}

type TagCleaningConfig struct {
	BrandNoiseWords    []string          `json:"brand_noise_words"`
	MarketingWords     []string          `json:"marketing_words"`
	MedicalTermMapping map[string]string `json:"medical_term_mapping"`
	MedicalCoreTerms   []string          `json:"medical_core_terms"`
	SpecPatterns       []string          `json:"spec_patterns"`
	UnitMapping        map[string]string `json:"unit_mapping"`
	MaxSpecLength      int               `json:"max_spec_length"`
}

type RuleEngineConfig struct {
	RuleFile          string `json:"rule_file"`
	UserRuleFile      string `json:"user_rule_file"`
	EnableDynamicLoad bool   `json:"enable_dynamic_load"`
}

type Config struct {
	DeepSeekAPIKey      string            `json:"deepseek_api_key"`
	MaxConcurrency      int               `json:"max_concurrency"`
	SensitiveWords      []string          `json:"sensitive_words"`
	LogLevel            string            `json:"log_level"`
	DatabasePath        string            `json:"database_path"`
	MaxUploadItems      int               `json:"max_upload_items"`
	MaxDownloadItems    int               `json:"max_download_items"`
	ApiOptimization     ApiOptimization   `json:"api_optimization"`
	RuleEngine          RuleEngineConfig  `json:"rule_engine"`
	EnableMultiCategory bool              `json:"enable_multi_category"`
	TagCleaning         TagCleaningConfig `json:"tag_cleaning"`
	BrandMappingFile    string            `json:"brand_mapping_file"` // 品牌映射文件路径
	BrandPool           []string          `json:"brand_pool"`         // 品牌池
	BrandSimilarity     float64           `json:"brand_similarity"`   // 相似度阈值 (0-1)
	Version             int               `json:"version"`
	DebugMode           bool              `json:"debug_mode"` // 添加调试模式开关
}

func GetConfig() *Config {
	configMutex.RLock()
	defer configMutex.RUnlock()
	return globalConfig
}

func InitConfig(path string) {
	ConfigPath = path
	loadConfig()
	go watchConfigChanges()
}

func loadConfig() {
	fmt.Printf(">>> 开始加载配置文件: %s\n", ConfigPath)
	startTime := time.Now()

	// 1. 检查文件是否存在
	if _, err := os.Stat(ConfigPath); os.IsNotExist(err) {
		fmt.Printf("!! 错误: 配置文件不存在 [%s]\n", ConfigPath)
		setDefaultConfig()
		fmt.Printf("<<< 加载完成 (耗时: %v, 原因: 文件不存在)\n", time.Since(startTime))
		return
	}

	// 2. 读取文件内容
	file, err := os.Open(ConfigPath)
	if err != nil {
		fmt.Printf("!! 配置文件打开失败: %v\n", err)
		setDefaultConfig()
		fmt.Printf("<<< 加载完成 (耗时: %v, 原因: 打开失败)\n", time.Since(startTime))
		return
	}
	defer file.Close()

	// 3. 读取原始内容并打印
	rawContent, err := io.ReadAll(file)
	if err != nil {
		fmt.Printf("!! 配置文件读取失败: %v\n", err)
		setDefaultConfig()
		fmt.Printf("<<< 加载完成 (耗时: %v, 原因: 读取失败)\n", time.Since(startTime))
		return
	}

	// 添加文件详细信息
	fileInfo, err := os.Stat(ConfigPath)
	if err != nil {
		fmt.Printf("!! 获取文件信息失败: %v\n", err)
	} else {
		fmt.Printf(">>> 文件信息: 大小=%d bytes, 修改时间=%v, 权限=%s\n",
			fileInfo.Size(),
			fileInfo.ModTime().Format(time.RFC3339),
			fileInfo.Mode().String())
	}

	fmt.Printf("=== 配置文件原始内容 ===\n%s\n=======================\n", string(rawContent))

	// 4. 解析JSON
	var newConfig Config
	if err := json.Unmarshal(rawContent, &newConfig); err != nil {
		fmt.Printf("!! 配置文件解析错误: %v\n", err)

		// 尝试更详细的错误定位
		if syntaxErr, ok := err.(*json.SyntaxError); ok {
			offset := syntaxErr.Offset
			line, col := findErrorLine(rawContent, offset)
			fmt.Printf("!! JSON语法错误位置: 行 %d, 列 %d (偏移量 %d)\n", line, col, offset)

			// 打印错误位置附近的上下文
			start := int(math.Max(0, float64(offset-20)))
			end := int(math.Min(float64(len(rawContent)), float64(offset+20)))
			fmt.Printf("!! 错误上下文: ...%s...\n", string(rawContent[start:end]))
		}

		setDefaultConfig()
		fmt.Printf("<<< 加载完成 (耗时: %v, 原因: 解析失败)\n", time.Since(startTime))
		return
	}

	// 5. 详细打印API密钥信息
	fmt.Printf(">>> 解析出的API密钥原始值: '%s'\n", newConfig.DeepSeekAPIKey)
	fmt.Printf(">>> API密钥长度: %d 字符\n", len(newConfig.DeepSeekAPIKey))

	// 检查密钥是否包含不可见字符
	hasInvisible := false
	for _, r := range newConfig.DeepSeekAPIKey {
		if r < 32 || r == 127 {
			hasInvisible = true
			break
		}
	}

	if hasInvisible {
		fmt.Println("!!! 警告: API密钥包含不可见字符")
		cleanedKey := strings.Map(func(r rune) rune {
			if r >= 32 && r != 127 {
				return r
			}
			return -1
		}, newConfig.DeepSeekAPIKey)
		fmt.Printf("!!! 清理后密钥: '%s' (长度: %d)\n", cleanedKey, len(cleanedKey))
		newConfig.DeepSeekAPIKey = cleanedKey
	}

	// 6. 版本校验
	if newConfig.Version != 1 {
		fmt.Printf("!! 配置版本不兼容 (期望版本1, 实际版本%d)\n", newConfig.Version)
		setDefaultConfig()
		fmt.Printf("<<< 加载完成 (耗时: %v, 原因: 版本不兼容)\n", time.Since(startTime))
		return
	}

	// 7. 环境变量回退机制
	if newConfig.DeepSeekAPIKey == "" {
		fmt.Println("!!! 警告: 配置文件中API密钥为空")
		if envKey := os.Getenv("DEEPSEEK_API_KEY"); envKey != "" {
			newConfig.DeepSeekAPIKey = envKey
			fmt.Println(">>> 使用环境变量中的API密钥")
		} else {
			fmt.Println("!!! 环境变量 DEEPSEEK_API_KEY 也未设置")
		}
	}

	// 8. 硬编码回退（仅用于调试）
	if newConfig.DeepSeekAPIKey == "" {
		// 仅在开发环境启用硬编码
		if os.Getenv("GO_ENV") == "development" {
			newConfig.DeepSeekAPIKey = "***********************************"
			fmt.Println("!!! 警告: 使用硬编码API密钥 (仅用于调试)")
		}
	}

	// 9. 打印解析后的完整配置结构
	fmt.Println("=== 解析后的配置结构 ===")
	fmt.Printf("Version: %d\n", newConfig.Version)
	fmt.Printf("DeepSeekAPIKey: '%s' (len:%d)\n",
		newConfig.DeepSeekAPIKey, len(newConfig.DeepSeekAPIKey))
	fmt.Printf("MaxConcurrency: %d\n", newConfig.MaxConcurrency)
	fmt.Printf("LogLevel: '%s'\n", newConfig.LogLevel)
	fmt.Printf("DatabasePath: '%s'\n", newConfig.DatabasePath)
	fmt.Printf("MaxUploadItems: %d\n", newConfig.MaxUploadItems)
	fmt.Printf("MaxDownloadItems: %d\n", newConfig.MaxDownloadItems)
	fmt.Printf("ApiOptimization.MaxSamples: %d\n", newConfig.ApiOptimization.MaxSamples)
	fmt.Printf("ApiOptimization.MinSimilarity: %.2f\n", newConfig.ApiOptimization.MinSimilarity)
	fmt.Printf("ApiOptimization.TokenLimit: %d\n", newConfig.ApiOptimization.TokenLimit)
	fmt.Printf("ApiOptimization.SpecStandardization: %v\n", newConfig.ApiOptimization.SpecStandardization)
	fmt.Printf("RuleEngine.RuleFile: '%s'\n", newConfig.RuleEngine.RuleFile)
	fmt.Printf("RuleEngine.UserRuleFile: '%s'\n", newConfig.RuleEngine.UserRuleFile)
	fmt.Printf("RuleEngine.EnableDynamicLoad: %v\n", newConfig.RuleEngine.EnableDynamicLoad)
	fmt.Printf("TagCleaning.BrandNoiseWords: %v\n", newConfig.TagCleaning.BrandNoiseWords)
	fmt.Printf("TagCleaning.MarketingWords: %v\n", newConfig.TagCleaning.MarketingWords)
	fmt.Printf("BrandMappingFile: %s\n", newConfig.BrandMappingFile) // 新增品牌映射文件路径打印
	fmt.Println("=====================")

	// 10. 设置默认值
	applyDefaultValues(&newConfig)

	// 11. 应用配置
	configMutex.Lock()
	globalConfig = &newConfig
	configMutex.Unlock()

	// 12. 最终验证
	if globalConfig.DeepSeekAPIKey == "" {
		fmt.Println("!!! 严重警告: DeepSeek API密钥未配置，AI功能将不可用 !!!")
	} else {
		fmt.Printf(">>> API密钥验证通过: %s\n", MaskAPIKey(globalConfig.DeepSeekAPIKey))
	}

	fmt.Println("✓ 配置文件加载完成")
	fmt.Printf("<<< 加载完成 (总耗时: %v)\n", time.Since(startTime))
}

// 辅助函数：定位JSON错误位置
func findErrorLine(data []byte, offset int64) (line, column int) {
	line = 1
	column = 1

	for i := int64(0); i < offset && i < int64(len(data)); i++ {
		if data[i] == '\n' {
			line++
			column = 1
		} else {
			column++
		}
	}
	return
}

// 应用默认值到配置
func applyDefaultValues(cfg *Config) {
	// 设置默认值
	if cfg.MaxConcurrency <= 0 {
		cfg.MaxConcurrency = 5
	}
	if len(cfg.SensitiveWords) == 0 {
		cfg.SensitiveWords = []string{"医疗器械", "家庭护理", "家庭用品", "日常用品", "最", "推荐"}
	}
	if cfg.LogLevel == "" {
		cfg.LogLevel = "info"
	}
	if cfg.MaxUploadItems <= 0 {
		cfg.MaxUploadItems = 1000
	}
	if cfg.MaxDownloadItems <= 0 {
		cfg.MaxDownloadItems = 5000
	}

	// API优化默认值
	if cfg.ApiOptimization.MaxSamples <= 0 {
		cfg.ApiOptimization.MaxSamples = 5
	}
	if cfg.ApiOptimization.MinSimilarity <= 0 {
		cfg.ApiOptimization.MinSimilarity = 0.6
	}
	if cfg.ApiOptimization.TokenLimit <= 0 {
		cfg.ApiOptimization.TokenLimit = 1500
	}
	if cfg.ApiOptimization.UnifiedTagThreshold == 0 {
		cfg.ApiOptimization.UnifiedTagThreshold = 0.8
	}
	if cfg.ApiOptimization.UnifiedSpecThreshold == 0 {
		cfg.ApiOptimization.UnifiedSpecThreshold = 0.85
	}
	if cfg.ApiOptimization.UnifiedNameThreshold == 0 {
		cfg.ApiOptimization.UnifiedNameThreshold = 0.8
	}

	// 规则引擎默认值
	if cfg.RuleEngine.RuleFile == "" {
		cfg.RuleEngine.RuleFile = "/Users/<USER>/GOLANG/allproject/tag-assistant/ai/rules.json"
	}
	if cfg.RuleEngine.UserRuleFile == "" {
		cfg.RuleEngine.UserRuleFile = "/Users/<USER>/GOLANG/allproject/tag-assistant/config/user_rules.json"
	}

	// 标签清洗默认配置
	if len(cfg.TagCleaning.SpecPatterns) == 0 {
		cfg.TagCleaning.SpecPatterns = []string{
			`\d+[xX*]\d+`,
			`\d+\.?\d*\s*(ml|毫升|g|克|mg|毫克|μg|微克|片|粒|瓶|袋|包|盒|个|套)`,
			"均码", "标准装", "常规装", `\d+片`,
		}
	}

	if len(cfg.TagCleaning.UnitMapping) == 0 {
		cfg.TagCleaning.UnitMapping = map[string]string{
			"袋": "袋", "包": "袋", "个": "个", "件": "个", "套": "套",
			"g": "g", "克": "g", "mg": "mg", "毫克": "mg",
			"ml": "ml", "毫升": "ml", "μg": "μg", "微克": "μg",
			"片": "片", "粒": "粒", "瓶": "瓶", "盒": "盒",
		}
	}

	// 设置规格最大长度默认值
	if cfg.TagCleaning.MaxSpecLength <= 0 {
		cfg.TagCleaning.MaxSpecLength = 20
	}

	if len(cfg.TagCleaning.BrandNoiseWords) == 0 {
		cfg.TagCleaning.BrandNoiseWords = []string{"正品", "专供", "旗舰店", "专卖", "厂家直销"}
	}
	if len(cfg.TagCleaning.MarketingWords) == 0 {
		cfg.TagCleaning.MarketingWords = []string{"新款", "升级版", "加强型", "特惠装", "大容量"}
	}
	if len(cfg.TagCleaning.MedicalTermMapping) == 0 {
		cfg.TagCleaning.MedicalTermMapping = map[string]string{
			"创口贴": "创可贴",
			"消毒液": "消毒剂",
			"棉签棒": "棉签",
			"卫生棉": "卫生巾",
		}
	}
}

func setDefaultConfig() {
	defaultConfig := &Config{
		Version:          1,
		MaxConcurrency:   5,
		SensitiveWords:   []string{"医疗器械", "家庭护理", "家庭用品", "日常用品", "最", "推荐"},
		LogLevel:         "info",
		MaxUploadItems:   1000,
		MaxDownloadItems: 5000,
		ApiOptimization: ApiOptimization{
			MaxSamples:          5,
			MinSimilarity:       0.6,
			TokenLimit:          1500,
			SpecStandardization: true,
		},
		RuleEngine: RuleEngineConfig{
			RuleFile:          "/Users/<USER>/GOLANG/allproject/tag-assistant/ai/rules.json",
			UserRuleFile:      "/Users/<USER>/GOLANG/allproject/tag-assistant/config/user_rules.json",
			EnableDynamicLoad: true,
		},
		EnableMultiCategory: true,
		DebugMode:           false, // 默认关闭调试模式
		TagCleaning: TagCleaningConfig{
			BrandNoiseWords: []string{"正品", "专供", "旗舰店", "专卖", "厂家直销"},
			MarketingWords:  []string{"新款", "升级版", "加强型", "特惠装", "大容量"},
			MedicalTermMapping: map[string]string{
				"创口贴": "创可贴",
				"消毒液": "消毒剂",
				"棉签棒": "棉签",
				"卫生棉": "卫生巾",
			},
			MaxSpecLength: 20,
		},
	}

	// 应用默认值确保完整
	applyDefaultValues(defaultConfig)

	configMutex.Lock()
	globalConfig = defaultConfig
	configMutex.Unlock()
	fmt.Println("! 使用默认配置")
}

func GetConfigPath() string {
	return ConfigPath
}

func LoadConfig() {
	loadConfig()
}

func MaskAPIKey(key string) string {
	if len(key) <= 4 {
		return "****"
	}
	return "****" + key[len(key)-4:]
}

func watchConfigChanges() {
	if ConfigPath == "" {
		fmt.Println("! 配置文件路径为空，无法监视")
		return
	}

	if _, err := os.Stat(ConfigPath); os.IsNotExist(err) {
		fmt.Printf("! 配置文件不存在，无法监视: %s\n", ConfigPath)
		return
	}

	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		fmt.Println("! 配置文件监视器创建失败:", err)
		return
	}
	defer watcher.Close()

	if err := watcher.Add(ConfigPath); err != nil {
		fmt.Printf("! 无法监视配置文件 (%s): %v\n", ConfigPath, err)
		return
	}

	fmt.Println("✓ 配置文件热加载已启用")

	for {
		select {
		case event, ok := <-watcher.Events:
			if !ok {
				return
			}
			if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
				fmt.Println("检测到配置文件变更，重新加载...")
				time.Sleep(100 * time.Millisecond) // 避免部分写入
				loadConfig()
			}
		case err, ok := <-watcher.Errors:
			if !ok {
				return
			}
			fmt.Println("配置文件监视错误:", err)
		}
	}
}
