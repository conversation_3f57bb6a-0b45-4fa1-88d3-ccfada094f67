[{"name": "耳塞统一规则", "description": "强制所有耳塞类产品使用统一标签和类目", "condition": "contains(productName, '耳塞') OR contains(coreTag, '耳塞')", "action": "SET", "priority": 100, "category2": ["睡眠改善"], "core_tag": "耳塞"}, {"name": "暖宝宝类产品", "description": "暖宝宝统一分到热敷类目", "condition": "contains(productName, '暖宝宝')", "action": "SET", "priority": 100, "category2": ["热敷/暖宫"], "core_tag": ""}, {"name": "抑菌抗菌类商品", "description": "抑菌/抗菌类商品（除手/足用外）归至皮肤护理", "condition": "(contains(coreTag, '抑菌') OR contains(coreTag, '抗菌')) AND !contains(productName, '手') AND !contains(productName, '足')", "action": "SET", "priority": 100, "category2": ["皮肤护理"]}, {"name": "洗手液类商品", "description": "核心标签含洗手液类商品归入家庭/护理", "condition": "contains(coreTag, '洗手液')", "action": "SET", "priority": 100, "category2": ["家庭/护理"]}, {"name": "纱布类商品", "description": "核心标签含纱布的商品归入纱布/胶带", "condition": "contains(coreTag, '纱布')", "action": "SET", "priority": 100, "category2": ["纱布/胶带"]}, {"name": "防水贴类商品", "description": "核心标签含防水贴、防水敷贴归入防护贴/酒精棉", "condition": "contains(coreTag, '防水贴') OR contains(coreTag, '防水敷贴')", "action": "SET", "priority": 100, "category2": ["防护贴/酒精棉"]}, {"name": "酒精棉类商品", "description": "酒精棉商品归入皮肤消毒", "condition": "contains(coreTag, '酒精棉')", "action": "SET", "priority": 100, "category2": ["皮肤消毒"]}, {"name": "隔离素颜霜类商品", "description": "隔离素颜霜归入彩妆护理", "condition": "contains(coreTag, '隔离素颜霜') OR contains(productName, '隔离素颜霜')", "action": "SET", "priority": 100, "category2": ["彩妆护理"]}, {"name": "婴儿人群专属类目", "description": "人群标签为婴儿的商品添加婴儿用品类目（排除特定核心标签）", "condition": "crowd = '婴儿' AND !contains(coreTag, '体温计') AND !contains(coreTag, '耳温枪') AND !contains(coreTag, '额温计') AND !contains(coreTag, '退热贴') AND !contains(coreTag, '退热凝胶') AND !contains(coreTag, '回奶茶') AND !contains(coreTag, '妇洗器') AND !contains(coreTag, '洗手液')", "action": "ADD", "priority": 100, "category2": ["婴儿用品"]}, {"name": "儿童人群专属类目", "description": "人群标签为婴儿的商品添加婴儿用品类目（排除特定核心标签）", "condition": "crowd = '婴儿' AND !contains(coreTag, '体温计') AND !contains(coreTag, '耳温枪') AND !contains(coreTag, '额温计') AND !contains(coreTag, '退热贴') AND !contains(coreTag, '退热凝胶') AND !contains(coreTag, '回奶茶') AND !contains(coreTag, '妇洗器') AND !contains(coreTag, '洗手液')", "action": "ADD", "priority": 100, "category2": ["婴儿用品"]}]