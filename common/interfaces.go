package common

import (
	"fmt"
)

// Product 通用商品接口
type Product interface {
	GetID() int
	GetName() string
	GetCoreTag() string
	GetScene() string
	GetCrowd() string
	GetBrand() string
	GetSpec() string
	GetCategory1() []string
	GetCategory2() []string
	GetCleanedName() string
	GetFeatureTags() []string
}

// ErrorCode 统一错误代码
type ErrorCode int

const (
	ErrDBConnection ErrorCode = iota + 1000
	ErrAIRequestFailed
	ErrConfigLoadFailed
	ErrInvalidInput
	ErrFileProcessing
)

// AppError 应用错误结构
type AppError struct {
	Code    ErrorCode
	Message string
	Details interface{}
}

func (e AppError) Error() string {
	return fmt.Sprintf("[%d] %s", e.Code, e.Message)
}

func NewError(code ErrorCode, msg string, details ...interface{}) error {
	return &AppError{
		Code:    code,
		Message: msg,
		Details: details,
	}
}
