package mapping

import (
	"sort"
	"strings"
)

// 特征标签同义词映射
var FeatureTagSynonyms = map[string]string{
	"凸点": "凸点型",
	// 可以在这里添加更多同义词映射
}

type Categorizable interface {
	GetCrowd() string
	GetCategory2() []string
	SetCategory1([]string)
	SetCategory2([]string)
}

// 完全按照Excel表格中的类目结构定义
var CategoryMapping = map[string]string{
	// 女性垫巾
	"女性垫巾": "女性垫巾",

	// 家庭护理
	"纱布/胶带":   "家庭护理",
	"家庭/护理":   "家庭护理",
	"皮肤消毒":    "家庭护理",
	"防护口罩":    "家庭护理",
	"绷带/固定带":  "家庭护理",
	"创可贴/创口贴": "家庭护理",
	"敷贴/敷料":   "家庭护理",
	"酒精棉/防水贴": "家庭护理", // 修正为与历史表述一致的"酒精棉/防水贴"

	// 一次性用品
	"湿巾/纸巾":   "一次性用品",
	"一次性辅助护理": "一次性用品", // 修正为原始二级类目的"一次性辅助护理"
	"一次性居家日用": "一次性用品",

	// 健康自测
	"测孕排卵试纸":  "健康自测",
	"流感病毒检测":  "健康自测",
	"艾滋/TP检测": "健康自测",
	"hpv检测":   "健康自测",
	"其他检测":    "健康自测",

	// 祛痘/疤痕
	"痘痘护理": "祛痘/疤痕",
	"疤痕护理": "祛痘/疤痕",
	"皮肤修复": "祛痘/疤痕",

	// 隐形眼镜
	"彩色美瞳": "隐形眼镜",
	"透明隐形": "隐形眼镜",
	"护理液":  "隐形眼镜",
	"眼镜配件": "隐形眼镜",

	// 身体护具
	"胸背护具": "身体护具",
	"颈部护具": "身体护具",
	"腰部护具": "身体护具",
	"腿部护具": "身体护具",
	"手部护具": "身体护具",
	"医用防护": "身体护具", // 修正为原始二级类目的"医用防护"

	// 耳鼻喉眼
	"鼻腔护理": "耳鼻喉眼",
	"眼部护理": "耳鼻喉眼",
	"口腔护理": "耳鼻喉眼",
	"耳部护理": "耳鼻喉眼",
	"咽喉护理": "耳鼻喉眼",

	// 健康护理
	"足部护理":  "健康护理",
	"头皮护理":  "健康护理",
	"脱毛/祛味": "健康护理",
	"肛周护理":  "健康护理",
	"睡眠改善":  "健康护理",

	// 美容护肤
	"医用护肤": "美容护肤",
	"面部护理": "美容护肤",
	"身体护理": "美容护肤",
	"唇部护理": "美容护肤",
	"肌肤保养": "美容护肤",
	"眼部保养": "美容护肤",
	"防晒止汗": "美容护肤",
	"面部清洁": "美容护肤",
	"彩妆护理": "美容护肤",
	"美容仪器": "美容护肤",

	// 营养保健
	"保健食品": "营养保健",
	"维生素":  "营养保健",
	"养生茶饮": "营养保健",
	"营养补充": "营养保健",

	// 中医理疗
	"理疗器械":  "中医理疗", // 修正为原始二级类目的"理疗器械"
	"按摩精油":  "中医理疗",
	"热敷/暖宫": "中医理疗",
	"煎药器材":  "中医理疗",
	"理疗贴":   "中医理疗",
	"艾灸/针灸": "中医理疗",

	// 退热退烧
	"退热用品": "退热退烧",
	"体温测量": "退热退烧",

	// 雾化/制氧/呼吸
	"制氧": "雾化/制氧/呼吸",
	"雾化": "雾化/制氧/呼吸",
	"呼吸": "雾化/制氧/呼吸",

	// 抑菌消痒
	"皮肤护理": "抑菌消痒",
	"手足护理": "抑菌消痒",
	"驱蚊消痒": "抑菌消痒",
	"私处护理": "抑菌消痒",

	// 外用膏贴
	"活血化瘀":  "外用膏贴",
	"止疼膏药":  "外用膏贴",
	"晕车贴":   "外用膏贴", // 简化为原始二级类目的"晕车贴"
	"辅助理疗贴": "外用膏贴",

	// 儿童健康
	"止咳贴":  "儿童健康",
	"儿童保健": "儿童健康",
	"婴儿用品": "儿童健康",

	// 女性健康
	"宝妈用品": "女性健康",
	"私处保养": "女性健康",
	"胸部护理": "女性健康",

	// 男性健康
	"男性护理": "男性健康",

	// 性福生活
	"避孕套":    "性福生活",
	"润滑液/喷剂": "性福生活",
	"玩具类":    "性福生活",
	"抗HPV":   "性福生活",

	// 日用百货
	"小家电":     "日用百货",
	"生活日用":    "日用百货",
	"沐浴露/润肤乳": "日用百货",
	"洗发护发":    "日用百货",

	// 轮椅拐杖
	"轮椅":   "轮椅拐杖",
	"腋拐肘拐": "轮椅拐杖",
	"坐便辅助": "轮椅拐杖",

	// 监测仪器
	"血糖监测":  "监测仪器",
	"胰岛素管理": "监测仪器",
	"血氧监测":  "监测仪器",
	"血压监测":  "监测仪器",
	"胎心监测":  "监测仪器",
	"其他监测":  "监测仪器",

	// 医疗器械
	"钳剪镊刀": "医疗器械",
	"急救设备": "医疗器械",
	"康复训练": "医疗器械",

	// 术后护理
	"造口护理": "术后护理",
	"肠胃护理": "术后护理",
	"泌尿护理": "术后护理",
	"康复辅助": "术后护理",
}

// 以下函数保持不变...
func GetCategory2(category1 string) []string {
	var result []string
	for cat2, cat1 := range CategoryMapping {
		if cat1 == category1 {
			result = append(result, cat2)
		}
	}
	return result
}

func GetCategory1(category2 string) string {
	if cat1, exists := CategoryMapping[category2]; exists {
		return cat1
	}
	return ""
}

func GetCategories1(categories2 []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, cat2 := range categories2 {
		if cat1 := GetCategory1(cat2); cat1 != "" && !seen[cat1] {
			seen[cat1] = true
			result = append(result, cat1)
		}
	}
	return result
}

func AreInSameCategory1(cat1, cat2 string) bool {
	return GetCategory1(cat1) == GetCategory1(cat2)
}

func ResolveCrowdConflict(crowd string, categories []string) []string {
	conflictMap := map[string][]string{
		"孕妇": {"男性护理", "避孕套", "玩具类"},
		"儿童": {"宝妈用品", "私处保养", "胸部护理"},
		"婴儿": {"避孕套", "玩具类", "男性护理"},
	}

	if forbidden, ok := conflictMap[crowd]; ok {
		var validCats []string
		for _, cat := range categories {
			valid := true
			for _, f := range forbidden {
				if cat == f {
					valid = false
					break
				}
			}
			if valid {
				validCats = append(validCats, cat)
			}
		}
		return validCats
	}
	return categories
}

func GetExpectedCategories(crowd string) []string {
	mapping := map[string][]string{
		"孕妇": {"宝妈用品"},
		"产妇": {"宝妈用品"},
		"婴儿": {"婴儿用品"},
		"儿童": {"儿童健康"},
	}
	if cats, ok := mapping[crowd]; ok {
		return cats
	}
	return nil
}

func AreCompatible(cat1, cat2 string) bool {
	incompatiblePairs := map[string][]string{
		"宝妈用品": {"男性护理", "避孕套", "玩具类"},
		"私处护理": {"轮椅", "拐杖"},
	}

	if forbidden, ok := incompatiblePairs[cat1]; ok {
		for _, f := range forbidden {
			if f == cat2 {
				return false
			}
		}
	}
	if forbidden, ok := incompatiblePairs[cat2]; ok {
		for _, f := range forbidden {
			if f == cat1 {
				return false
			}
		}
	}
	return true
}

func FilterCategories(crowd string, categories []string) []string {
	if len(categories) == 0 {
		return categories
	}

	categories = ResolveCrowdConflict(crowd, categories)

	if len(categories) <= 1 {
		return categories
	}

	seen := make(map[string]bool)
	var result []string

	for _, cat2 := range categories {
		cat1 := GetCategory1(cat2)
		if cat1 == "" {
			continue
		}

		if !seen[cat1] {
			seen[cat1] = true
			result = append(result, cat2)
		}

		if len(result) >= 2 {
			break
		}
	}

	if len(result) == 2 {
		if !AreCompatible(result[0], result[1]) {
			result = []string{result[0]}
		}
	}

	return result
}

func GetCategoryMapping(categories2 []string) map[string]string {
	mapping := make(map[string]string)
	for _, cat2 := range categories2 {
		if cat1 := GetCategory1(cat2); cat1 != "" {
			mapping[cat2] = cat1
		}
	}
	return mapping
}

func GetAllValidCategory2() []string {
	var categories []string
	for cat := range CategoryMapping {
		categories = append(categories, cat)
	}
	sort.Strings(categories)
	return categories
}

// 标准化特征标签
func NormalizeFeatureTag(tag string) string {
	tag = strings.TrimSpace(tag)
	if normalized, exists := FeatureTagSynonyms[tag]; exists {
		return normalized
	}
	return tag
}

func IsValidCategory2(category2 string) bool {
	_, exists := CategoryMapping[category2]
	return exists
}

func ValidateCategory(category string) bool {
	_, exists := CategoryMapping[category]
	return exists
}

func AutoMapCategories(p Categorizable) {
	categories := p.GetCategory2()
	if len(categories) == 0 {
		return
	}
	categoryMap := make(map[string]bool)
	var category1 []string
	for _, cat2 := range categories {
		if cat1 := GetCategory1(cat2); cat1 != "" {
			if !categoryMap[cat1] {
				categoryMap[cat1] = true
				category1 = append(category1, cat1)
			}
		}
	}
	p.SetCategory1(category1)
	if crowd := p.GetCrowd(); crowd != "" {
		expected := GetExpectedCategories(crowd)
		currentCats := p.GetCategory2()

		for _, expCat := range expected {
			found := false
			for _, cat := range currentCats {
				if cat == expCat {
					found = true
					break
				}
			}

			if !found {
				currentCats = append(currentCats, expCat)
				if cat1 := GetCategory1(expCat); cat1 != "" && !categoryMap[cat1] {
					categoryMap[cat1] = true
					category1 = append(category1, cat1)
				}
			}
		}
		p.SetCategory2(currentCats)
		p.SetCategory1(category1)
	}
}
