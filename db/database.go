package db

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	// 中文分词器 - 需要在项目中导入或实现
	_ "github.com/mattn/go-sqlite3"
	"github.com/mogan/tag-assistant/config"
	"github.com/mogan/tag-assistant/db/mapping"
)

// 实现common.Product接口
func (p *Product) GetID() int               { return p.ID }
func (p *Product) GetName() string          { return p.ProductName }
func (p *Product) GetCoreTag() string       { return p.CoreTag }
func (p *Product) GetScene() string         { return p.Scene }
func (p *Product) GetBrand() string         { return p.Brand }
func (p *Product) GetSpec() string          { return p.Spec }
func (p *Product) GetCategory1() []string   { return p.Category1 }
func (p *Product) GetCleanedName() string   { return p.CleanedName }
func (p *Product) GetFeatureTags() []string { return p.FeatureTags }
func (p *Product) GetCrowd() string         { return p.Crowd }
func (p *Product) GetCategory2() []string   { return p.Category2 }

// 自动映射类目（增强版）
func (p *Product) AutoMapCategories() {
	if len(p.Category2) == 0 {
		return
	}

	// 初始化Category1
	p.Category1 = []string{}

	// 获取一级类目映射
	categoryMap := make(map[string]bool)

	// 将for循环移到方法内部
	for _, cat2 := range p.Category2 {
		if cat1 := mapping.GetCategory1(cat2); cat1 != "" {
			if !categoryMap[cat1] {
				categoryMap[cat1] = true
				p.Category1 = append(p.Category1, cat1)
			}
		}
	}

	// 特殊人群强制类目检查
	if p.Crowd != "" {
		expected := mapping.GetExpectedCategories(p.Crowd)
		found := false
		for _, exp := range expected {
			if containsString(p.Category2, exp) {
				found = true
				break
			}
		}

		if !found && len(expected) > 0 {
			if config.GetConfig().DebugMode {
				fmt.Printf("审计告警: %s 缺少%s类目，自动添加\n",
					p.ProductName, strings.Join(expected, "/"))
			}
			// 自动添加缺失的类目
			p.Category2 = append(p.Category2, expected...)
		}
	}

	// 类目过滤和映射
	p.Category2 = mapping.FilterCategories(p.Crowd, p.Category2)

	if len(p.Category2) > 0 && len(p.Category1) == 0 {
		p.Category1 = mapping.GetCategories1(p.Category2)
	}
}

// 在Product结构体下添加方法实现接口
func (p *Product) SetCategory1(cats []string) {
	p.Category1 = cats
}
func (p *Product) SetCategory2(cats []string) {
	p.Category2 = cats
}

// 辅助函数：检查字符串是否在切片中
func containsString(slice []string, str string) bool {
	for _, s := range slice {
		if s == str {
			return true
		}
	}
	return false
}

type Product struct {
	ID          int      `json:"id"`
	ProductName string   `json:"product_name"`
	CoreTag     string   `json:"core_tag"`
	Scene       string   `json:"scene"`
	Crowd       string   `json:"crowd"`
	Brand       string   `json:"brand"`
	Spec        string   `json:"spec"`
	Category1   []string `json:"category1"`
	Category2   []string `json:"category2"`
	CleanedName string   `json:"cleaned_name"` // 修复这里 - 移除多余的引号
	FeatureTags []string `json:"feature_tags"` // 新增特征标签字段
}

var DB *sql.DB
var DatabasePath string

var (
	writeQueue = make(chan func() error, 100) // 数据库写队列（返回错误）
	queueOnce  sync.Once
	queueWg    sync.WaitGroup // 用于等待所有任务完成
)

// 启动数据库写队列处理器
func initWriteQueue() {
	queueOnce.Do(func() {
		go func() {
			for task := range writeQueue {
				if err := task(); err != nil {
					log.Printf("数据库队列任务执行失败: %v", err)
				}
				queueWg.Done()
			}
		}()
	})
}

// file: database.go
func InitDB() {
	cfg := config.GetConfig()
	dbPath := "tag_database.db"
	if cfg.DatabasePath != "" {
		dbPath = cfg.DatabasePath
	}

	absPath, err := filepath.Abs(dbPath)
	if err != nil {
		log.Fatalf("获取数据库绝对路径失败: %v", err)
	}
	DatabasePath = absPath

	// 打印更详细的路径信息
	fmt.Printf("✓ 数据库文件路径: %s\n", absPath)
	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		fmt.Println("! 数据库文件不存在，将创建新数据库")
	} else {
		fmt.Println("✓ 数据库文件已存在")
		// 新增权限修复
		fixFilePermissions()
	}

	dsn := absPath + "?_busy_timeout=5000&_journal_mode=WAL&_sync=NORMAL"
	DB, err = sql.Open("sqlite3", dsn)
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 添加连接健康检查和重连机制
	go func() {
		for {
			time.Sleep(30 * time.Second)
			if err := DB.Ping(); err != nil {
				fmt.Printf("数据库连接异常: %v，尝试重新连接...\n", err)
				ReconnectDB()
			}
		}
	}()

	// 尝试连接3次
	for i := 0; i < 3; i++ {
		err = DB.Ping()
		if err == nil {
			break
		}
		log.Printf("数据库连接尝试 %d/3 失败: %v", i+1, err)
		time.Sleep(1 * time.Second)
	}
	if err != nil {
		log.Fatal("无法建立数据库连接:", err)
	}

	DB.SetMaxOpenConns(25)
	DB.SetMaxIdleConns(10)
	DB.SetConnMaxLifetime(10 * time.Minute)

	// 添加详细的表创建日志
	if err := checkAndCreateTable(); err != nil {
		log.Fatal("数据库初始化失败:", err)
	} else {
		if config.GetConfig().DebugMode {
			fmt.Println("✓ 数据库表检查完成")
		}
	}

	initWriteQueue() // 启动写队列

	fmt.Println("✓ 数据库初始化完成")

	// 打印商品数量
	count := GetProductCount()
	if count >= 0 {
		fmt.Printf("✓ 当前数据库中有 %d 条商品记录\n", count)
	}
}

// 新增：重新连接数据库
// file: database.go
func ReconnectDB() {
	if DB != nil {
		DB.Close()
		fmt.Println("已关闭旧数据库连接")
	}

	dsn := DatabasePath + "?_busy_timeout=5000&_journal_mode=WAL&_sync=NORMAL"
	newDB, err := sql.Open("sqlite3", dsn)
	if err != nil {
		fmt.Printf("重新连接数据库失败: %v\n", err)
		return
	}

	// 设置连接池参数
	newDB.SetMaxOpenConns(25)
	newDB.SetMaxIdleConns(10)
	newDB.SetConnMaxLifetime(10 * time.Minute)

	// 尝试Ping
	for i := 0; i < 3; i++ {
		err = newDB.Ping()
		if err == nil {
			break
		}
		time.Sleep(1 * time.Second)
	}
	if err != nil {
		fmt.Printf("重新连接数据库后Ping失败: %v\n", err)
		return
	}

	DB = newDB
	fmt.Println("✓ 数据库重新连接成功")

	// 重新检查表结构
	if err := checkAndCreateTable(); err != nil {
		fmt.Printf("! 表结构检查失败: %v\n", err)
	}
}

// 修复数据库文件权限，确保可写
func fixFilePermissions() {
	filePath := DatabasePath
	info, err := os.Stat(filePath)
	if err != nil {
		log.Printf("权限检查失败: %v", err)
		return
	}

	// 当前权限模式
	currentMode := info.Mode()
	// 添加用户组写权限（0200表示用户组写权限）
	newMode := currentMode | 0200

	// 如果当前权限已经包含用户组写权限，则无需修改
	if currentMode&0200 != 0 {
		log.Println("✓ 数据库文件已有写权限")
		return
	}

	// 修改权限
	err = os.Chmod(filePath, newMode)
	if err != nil {
		log.Printf("权限修复失败: %v", err)
	} else {
		log.Printf("权限已修复: %s -> %s", currentMode, newMode)
	}
}

func GetDatabasePath() string {
	return DatabasePath
}

func DatabaseFileExists() bool {
	_, err := os.Stat(DatabasePath)
	return !os.IsNotExist(err)
}

// file: database.go
func GetProductCount() int {
	if DB == nil {
		log.Println("! 数据库连接未初始化")
		return -1
	}

	var count int
	// 添加重试机制
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		err := DB.QueryRow("SELECT COUNT(*) FROM products").Scan(&count)
		if err == nil {
			return count
		}
		log.Printf("获取商品数量失败(尝试 %d/%d): %v", i+1, maxRetries, err)
		time.Sleep(500 * time.Millisecond)
	}
	log.Println("! 多次尝试后仍无法获取商品数量")
	return -1
}

// 检查商品是否已存在（基于商品名称和二级类目）
func ProductExists(name string, category2 []string) (bool, error) {
	cat2Str := strings.Join(category2, ",")

	var exists bool
	query := `SELECT EXISTS(
        SELECT 1 FROM products 
        WHERE product_name = ? AND category2 = ?
    )`

	err := DB.QueryRow(query, name, cat2Str).Scan(&exists)
	if err != nil {
		return false, err
	}
	return exists, nil
}

// file: database.go
func checkAndCreateTable() error {
	// 添加详细的表存在性检查
	var tableName string
	err := DB.QueryRow("SELECT name FROM sqlite_master WHERE type='table' AND name='products'").Scan(&tableName)
	if err != nil && err != sql.ErrNoRows {
		return fmt.Errorf("检查表存在失败: %v", err)
	}

	if tableName == "" {
		fmt.Println("! products表不存在，正在创建...")
		if err := createNewTable(); err != nil {
			return err
		}
		fmt.Println("✓ products表创建成功")
	} else {
		fmt.Println("✓ products表已存在")
	}

	// 检查并添加缺失的列
	columns, err := getTableColumns()
	if err != nil {
		return fmt.Errorf("获取表结构失败: %v", err)
	}

	requiredColumns := []string{"spec", "category1", "category2", "cleaned_name", "feature_tags"}
	for _, col := range requiredColumns {
		if !containsColumn(columns, col) {
			fmt.Printf("! 检测到缺失列: %s\n", col)
			if err := addColumn(col, "TEXT"); err != nil {
				return fmt.Errorf("添加 %s 列失败: %v", col, err)
			}
			fmt.Printf("✓ 已添加缺失的 %s 列\n", col)
		}
	}

	return nil
}

func createNewTable() error {
	createTableSQL := `
    CREATE TABLE products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_name TEXT NOT NULL,
        core_tag TEXT,
        scene TEXT,
        crowd TEXT,
        brand TEXT,
        spec TEXT,
        category1 TEXT,
        category2 TEXT,
        cleaned_name TEXT,
        feature_tags TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- 添加唯一约束
        UNIQUE(product_name, category2)
    );`

	_, err := DB.Exec(createTableSQL)
	return err
}

func getTableColumns() ([]string, error) {
	rows, err := DB.Query("PRAGMA table_info(products)")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var columns []string
	for rows.Next() {
		var cid int
		var name string
		var dataType string
		var notNull int
		var dfltValue interface{}
		var pk int
		if err := rows.Scan(&cid, &name, &dataType, &notNull, &dfltValue, &pk); err != nil {
			return nil, err
		}
		columns = append(columns, name)
	}
	return columns, nil
}

func containsColumn(columns []string, name string) bool {
	for _, col := range columns {
		if col == name {
			return true
		}
	}
	return false
}

func addColumn(columnName, columnType string) error {
	_, err := DB.Exec(fmt.Sprintf(`
		ALTER TABLE products 
		ADD COLUMN %s %s
	`, columnName, columnType))
	return err
}

// file: database.go
func BatchInsertProducts(products []Product) error {
	// === 增强监控：数据库插入开始 ===
	log.Printf("🗄️ [数据库监控] 开始批量插入 %d 条商品记录", len(products))

	// 品牌统计监控
	brandStats := make(map[string]int)
	emptyBrandCount := 0
	for i, p := range products {
		if p.Brand == "" || p.Brand == "无品牌" {
			emptyBrandCount++
			log.Printf("[数据库监控] 商品%d品牌为空: '%s'", i+1, p.ProductName)
		} else {
			brandStats[p.Brand]++
		}
	}
	log.Printf("[数据库监控] 品牌统计 - 空品牌: %d, 有效品牌种类: %d", emptyBrandCount, len(brandStats))

	// 新增：检查二级类目与商品名称一致的记录
	var filteredProducts []Product
	specialDuplicateCount := 0
	for _, p := range products {
		// 检查二级类目是否与商品名称完全一致
		if len(p.Category2) == 1 && p.Category2[0] == p.ProductName {
			specialDuplicateCount++
			if config.GetConfig().DebugMode {
				log.Printf("跳过二级类目与商品名称相同的商品: %s - %v", p.ProductName, p.Category2)
			}
			continue
		}
		filteredProducts = append(filteredProducts, p)
	}

	// 去重后的商品列表
	var uniqueProducts []Product
	duplicateCount := 0

	// 第一步：应用内去重
	seen := make(map[string]bool)
	for _, p := range filteredProducts {
		// 生成增强唯一键：包含商品名称和类目一致性标记
		categoryMatch := "0"
		if containsString(p.Category2, p.ProductName) {
			categoryMatch = "1"
		}
		key := p.ProductName + "|" + strings.Join(p.Category2, ",") + "|" + categoryMatch

		if seen[key] {
			if config.GetConfig().DebugMode {
				log.Printf("跳过重复商品 (应用内去重): %s - %v", p.ProductName, p.Category2)
			}
			duplicateCount++
			continue
		}
		seen[key] = true
		uniqueProducts = append(uniqueProducts, p)
	}

	// 第二步：数据库去重
	var finalProducts []Product
	for _, p := range uniqueProducts {
		exists, err := ProductExists(p.ProductName, p.Category2)
		if err != nil {
			log.Printf("检查商品存在性失败: %v", err)
			continue
		}
		if exists {
			if config.GetConfig().DebugMode {
				log.Printf("跳过重复商品 (数据库去重): %s - %v", p.ProductName, p.Category2)
			}
			duplicateCount++
			continue
		}
		finalProducts = append(finalProducts, p)
	}

	totalDuplicate := duplicateCount + specialDuplicateCount
	log.Printf("过滤后: 原始 %d, 总去重 %d (类目一致: %d, 常规重复: %d), 最终 %d",
		len(products), totalDuplicate, specialDuplicateCount, duplicateCount, len(finalProducts))

	if len(finalProducts) == 0 {
		log.Println("所有商品均为重复项，无需插入")
		return nil
	}

	tx, err := DB.Begin()
	if err != nil {
		log.Printf("开启事务失败: %v", err)
		return err
	}
	defer tx.Rollback()

	stmt, err := tx.Prepare(`
        INSERT INTO products (
            product_name, core_tag, scene, crowd, 
            brand, spec, category1, category2, cleaned_name, feature_tags
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)
	if err != nil {
		log.Printf("准备SQL语句失败: %v", err)
		return err
	}
	defer stmt.Close()

	var successCount int
	for i := range finalProducts {
		GenerateCleanedName(&finalProducts[i])

		p := &finalProducts[i]
		p.Category2 = mapping.FilterCategories(p.Crowd, p.Category2)

		if len(p.Category2) > 0 && len(p.Category1) == 0 {
			p.Category1 = mapping.GetCategories1(p.Category2)
		}

		// 添加详细日志
		log.Printf("插入商品: %s", p.ProductName)
		fmt.Printf("[监控-DB-1] 准备插入数据库 - 产品: '%s', 品牌: '%s'\n", p.ProductName, p.Brand)

		if _, err := stmt.Exec(
			p.ProductName, p.CoreTag, p.Scene, p.Crowd,
			p.Brand, p.Spec,
			strings.Join(p.Category1, ","),
			strings.Join(p.Category2, ","),
			p.CleanedName,
			strings.Join(p.FeatureTags, ","),
		); err != nil {
			// 处理唯一约束冲突错误
			if strings.Contains(err.Error(), "UNIQUE constraint failed") {
				log.Printf("唯一约束冲突: %s - %v", p.ProductName, p.Category2)
				continue
			}
			log.Printf("插入商品失败: %s, 错误: %v", p.ProductName, err)
			return err
		}
		successCount++
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		log.Printf("事务提交失败: %v", err)
		return err
	}

	log.Printf("批量插入完成, 成功插入 %d/%d 条记录", successCount, len(finalProducts))
	return nil
}

// 修改：添加重试机制和错误处理
func GetAllProducts() ([]Product, error) {
	// 重试机制
	var products []Product
	var err error
	maxRetries := 3

	for i := 0; i < maxRetries; i++ {
		products, err = getAllProductsOnce()
		if err == nil {
			return products, nil
		}
		log.Printf("获取所有商品失败(尝试 %d/%d): %v", i+1, maxRetries, err)
		time.Sleep(1 * time.Second)
	}
	return nil, fmt.Errorf("获取所有商品失败: %w", err)
}

// 通过唯一键检查商品是否存在
func CheckProductExistsByUniqueKey(uniqueKey string) (bool, error) {
	if DB == nil {
		return false, fmt.Errorf("数据库连接未初始化")
	}

	var exists bool
	query := `SELECT EXISTS(SELECT 1 FROM products WHERE unique_key = ?)`
	err := DB.QueryRow(query, uniqueKey).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("查询商品存在性失败: %w", err)
	}
	return exists, nil
}

// 实际的查询函数
func getAllProductsOnce() ([]Product, error) {
	rows, err := DB.Query(`
		SELECT 
			id, 
			product_name, 
			COALESCE(core_tag, '') AS core_tag,
			COALESCE(scene, '') AS scene,
			COALESCE(crowd, '') AS crowd,
			COALESCE(brand, '') AS brand,
			COALESCE(spec, '') AS spec,
			COALESCE(category1, '') AS category1,
			COALESCE(category2, '') AS category2,
			COALESCE(cleaned_name, '') AS cleaned_name,
			COALESCE(feature_tags, '') AS feature_tags
		FROM products
		ORDER BY id
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var products []Product
	for rows.Next() {
		var p Product
		var cat1Str, cat2Str, featureTagsStr string
		err := rows.Scan(
			&p.ID, &p.ProductName, &p.CoreTag, &p.Scene, &p.Crowd,
			&p.Brand, &p.Spec, &cat1Str, &cat2Str, &p.CleanedName, &featureTagsStr,
		)
		if err != nil {
			return nil, err
		}

		// 数据库读取品牌监控
		if p.ID <= 5 { // 只监控前5个产品，避免日志过多
			fmt.Printf("[监控-DB-READ] 从数据库读取 - 产品: '%s', 品牌: '%s'\n", p.ProductName, p.Brand)
		}

		p.Category1 = splitCategoryString(cat1Str)
		p.Category2 = splitCategoryString(cat2Str)
		p.FeatureTags = splitCategoryString(featureTagsStr) // 解析特征标签

		products = append(products, p)
	}

	// 检查迭代过程中是否有错误
	if err := rows.Err(); err != nil {
		return nil, err
	}

	return products, nil
}

func GetProductByName(name string) (Product, error) {
	var p Product
	var cat1Str, cat2Str, featureTagsStr string
	err := DB.QueryRow(`
		SELECT 
			id, 
			product_name, 
			COALESCE(core_tag, '') AS core_tag,
			COALESCE(scene, '') AS scene,
			COALESCE(crowd, '') AS crowd,
			COALESCE(brand, '') AS brand,
			COALESCE(spec, '') AS spec,
			COALESCE(category1, '') AS category1,
			COALESCE(category2, '') AS category2,
			COALESCE(cleaned_name, '') AS cleaned_name,
			COALESCE(feature_tags, '') AS feature_tags
		FROM products 
		WHERE product_name = ?
	`, name).Scan(
		&p.ID, &p.ProductName, &p.CoreTag, &p.Scene, &p.Crowd,
		&p.Brand, &p.Spec, &cat1Str, &cat2Str, &p.CleanedName, &featureTagsStr,
	)

	if err != nil {
		return Product{}, err
	}

	p.Category1 = splitCategoryString(cat1Str)
	p.Category2 = splitCategoryString(cat2Str)
	p.FeatureTags = splitCategoryString(featureTagsStr) // 解析特征标签

	return p, nil
}

func UpdateProduct(p Product) error {
	p.Category2 = mapping.FilterCategories(p.Crowd, p.Category2)
	if len(p.Category2) > 0 && len(p.Category1) == 0 {
		p.Category1 = mapping.GetCategories1(p.Category2)
	}
	GenerateCleanedName(&p)

	_, err := DB.Exec(`
		UPDATE products 
		SET 
			product_name = ?,
			core_tag = ?,
			scene = ?,
			crowd = ?,
			brand = ?,
			spec = ?,
			category1 = ?,
			category2 = ?,
			cleaned_name = ?,
			feature_tags = ?
		WHERE id = ?
	`, p.ProductName, p.CoreTag, p.Scene, p.Crowd, p.Brand, p.Spec,
		strings.Join(p.Category1, ","),
		strings.Join(p.Category2, ","),
		p.CleanedName,
		strings.Join(p.FeatureTags, ","), // 保存特征标签
		p.ID)

	return err
}

func GetDistinctCoreTagSamples(limit int) ([]Product, error) {
	rows, err := DB.Query(`
		SELECT 
			core_tag,
			product_name
		FROM (
			SELECT 
				core_tag,
				product_name,
				ROW_NUMBER() OVER (PARTITION BY core_tag ORDER BY RANDOM()) as rn
			FROM products
			WHERE core_tag != ''
		)
		WHERE rn = 1
		ORDER BY core_tag COLLATE NOCASE
		LIMIT ?`, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var products []Product
	for rows.Next() {
		var p Product
		if err := rows.Scan(&p.CoreTag, &p.ProductName); err != nil {
			return nil, err
		}
		products = append(products, p)
	}
	return products, nil
}

func StartConnectionPoolMonitor() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		stats := DB.Stats()
		log.Printf("DB 连接池状态: 打开连接数=%d 使用中=%d 空闲=%d 等待=%d",
			stats.OpenConnections,
			stats.InUse,
			stats.Idle,
			stats.WaitCount)
	}
}

func GetCoreTagMap() (map[string]string, error) {
	rows, err := DB.Query(`
		SELECT product_name, core_tag 
		FROM products
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	tagMap := make(map[string]string)
	for rows.Next() {
		var name, tag string
		if err := rows.Scan(&name, &tag); err != nil {
			return nil, err
		}
		tagMap[name] = tag
	}
	return tagMap, nil
}

func GetTopProducts(limit int) ([]Product, error) {
	rows, err := DB.Query(`
		SELECT 
			id, 
			product_name, 
			COALESCE(core_tag, '') AS core_tag,
			COALESCE(scene, '') AS scene,
			COALESCE(crowd, '') AS crowd,
			COALESCE(brand, '') AS brand,
			COALESCE(spec, '') AS spec,
			COALESCE(category1, '') AS category1,
			COALESCE(category2, '') AS category2,
			COALESCE(cleaned_name, '') AS cleaned_name,
			COALESCE(feature_tags, '') AS feature_tags
		FROM products
		ORDER BY id DESC
		LIMIT ?`, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var products []Product
	for rows.Next() {
		var p Product
		var cat1Str, cat2Str, featureTagsStr string
		if err := rows.Scan(
			&p.ID, &p.ProductName, &p.CoreTag, &p.Scene, &p.Crowd,
			&p.Brand, &p.Spec, &cat1Str, &cat2Str, &p.CleanedName, &featureTagsStr,
		); err != nil {
			return nil, err
		}

		p.Category1 = splitCategoryString(cat1Str)
		p.Category2 = splitCategoryString(cat2Str)
		p.FeatureTags = splitCategoryString(featureTagsStr) // 解析特征标签

		products = append(products, p)
	}
	return products, nil
}

func IsMigrationDone() bool {
	if DB == nil {
		log.Println("数据库连接未初始化")
		return false
	}

	// 简化迁移状态检查
	var done int
	err := DB.QueryRow("SELECT done FROM migration_status WHERE id=1").Scan(&done)
	if err != nil {
		if err == sql.ErrNoRows {
			return false
		}
		log.Printf("迁移状态查询失败: %v", err)
		return false
	}
	return done == 1
}

func MarkMigrationDone() {
	DB.Exec(`CREATE TABLE IF NOT EXISTS migration_status (
        id INTEGER PRIMARY KEY,
        done INTEGER NOT NULL DEFAULT 0
    )`)

	DB.Exec(`INSERT OR REPLACE INTO migration_status (id, done) VALUES (1, 1)`)
}

func MigrateCategories() error {
	rows, err := DB.Query("SELECT id, category2 FROM products WHERE category2 != ''")
	if err != nil {
		return fmt.Errorf("查询需要迁移的商品失败: %v", err)
	}
	defer rows.Close()

	tx, err := DB.Begin()
	if err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	updateStmt, err := tx.Prepare("UPDATE products SET category1 = ? WHERE id = ?")
	if err != nil {
		return fmt.Errorf("准备更新语句失败: %v", err)
	}
	defer updateStmt.Close()

	for rows.Next() {
		var id int
		var category2 string
		if err := rows.Scan(&id, &category2); err != nil {
			log.Printf("读取商品记录失败: %v", err)
			continue
		}

		category1 := mapping.GetCategory1(category2)
		if category1 == "" {
			log.Printf("警告: 未找到二级类目 %s 对应的一级类目", category2)
			continue
		}

		if _, err := updateStmt.Exec(category1, id); err != nil {
			log.Printf("更新商品ID %d 失败: %v", id, err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	_, err = DB.Exec(`
		UPDATE products 
		SET category1 = '', category2 = ''
		WHERE category2 != '' AND category1 = ''
	`)
	return err
}

func ClearAllProducts() error {
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	tx, err := DB.Begin()
	if err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	_, err = tx.Exec("DELETE FROM products")
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("清空商品表失败: %v", err)
	}

	_, err = tx.Exec("DELETE FROM sqlite_sequence WHERE name='products'")
	if err != nil {
		fmt.Println("警告: 重置自增ID失败，但不影响操作:", err)
	}

	_, err = tx.Exec("DELETE FROM migration_status")
	if err != nil {
		fmt.Println("警告: 清空迁移状态表失败，但不影响操作:", err)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	_, err = DB.Exec("VACUUM")
	if err != nil {
		fmt.Println("警告: 数据库优化失败，但不影响操作:", err)
	}

	return nil
}

func splitCategoryString(s string) []string {
	if s == "" {
		return []string{}
	}
	return strings.Split(s, ",")
}

// 规格黑名单 - 需要过滤的无效规格模式
var specBlacklist = []string{
	// 促销信息
	`买\d+送\d+`, `\d+送\d+`, `送\d+`, `买\d+`, `赠\d+`,
	`特惠`, `优惠`, `折扣`, `活动`, `限时`, `秒杀`,
	`\d+盒送\d+盒`, `\d+盒送\d+`, `送.*盒`,

	// 年份信息
	`20\d{2}年?`, `\d{2}年`, `年份`, `有效期`,

	// 产品型号/编码
	`SP\d+`, `型号`, `编号`, `货号`, `款式`, `\d+型`,

	// 包装描述词
	`装$`, `包装`, `外包装`, `内包装`, `天使装`, `双条装`, `盒装`,
	`双笔`, `笔装`, `条装`,

	// 企业信息
	`企业`, `公司`, `有限`, `集团`, `厂家`,

	// 其他无效信息
	`含量[＞>]`, `浓度`, `纯度`, `成分`,

	// 特殊字符组合
	`^[\d\s\-\*\+\=\(\)\[\]【】（）]*$`,
}

// 检查是否为无效规格
func isInvalidSpec(spec string) bool {
	if spec == "" {
		return true
	}

	// 检查黑名单
	for _, pattern := range specBlacklist {
		if matched, _ := regexp.MatchString(pattern, spec); matched {
			return true
		}
	}

	// 检查是否只包含特殊字符或空格
	if matched, _ := regexp.MatchString(`^[\s\-\*\+\=\(\)\[\]【】（）]*$`, spec); matched {
		return true
	}

	// 检查是否过长（可能是描述而非规格）
	if len(spec) > 50 {
		return true
	}

	return false
}

// 规格质量评分
func scoreSpecification(spec string) int {
	score := 0

	// 包含数字+单位的规格得分最高
	if matched, _ := regexp.MatchString(`\d+\.?\d*\s*(?:ml|g|mg|μg|l|kg|cm|mm|m|%)`, spec); matched {
		score += 10
	}

	// 包含数量单位的规格
	if matched, _ := regexp.MatchString(`\d+\.?\d*\s*(?:片|粒|支|个|只|袋|包|瓶|盒|套|条|贴|枚|罐|筒)`, spec); matched {
		score += 8
	}

	// 包含尺寸信息的规格
	if matched, _ := regexp.MatchString(`\d+\.?\d*\s*[xX*×]\s*\d+\.?\d*`, spec); matched {
		score += 7
	}

	// 包含人份信息的规格 - 提高优先级
	if matched, _ := regexp.MatchString(`\d*人份`, spec); matched {
		score += 9
	}

	// 包含包装信息的复合规格
	if matched, _ := regexp.MatchString(`/(?:盒|包|袋|瓶|支|罐|筒|套)`, spec); matched {
		score += 3
	}

	// 文字规格
	if matched, _ := regexp.MatchString(`均码|标准装|常规装|大号|中号|小号`, spec); matched {
		score += 5
	}

	// 长度惩罚
	if len(spec) > 20 {
		score -= 2
	}

	// 如果包含促销信息，大幅降分
	if matched, _ := regexp.MatchString(`送|买|赠|特惠|优惠`, spec); matched {
		score -= 10
	}

	return score
}

// 增强规格提取函数
func ExtractSpecification(name string) string {
	// 清理输入字符串
	cleanName := strings.ReplaceAll(name, "／", "/")
	cleanName = strings.ReplaceAll(cleanName, "×", "x")
	cleanName = strings.ReplaceAll(cleanName, "*", "x")

	// 扩展的匹配模式，按优先级排序
	patterns := []struct {
		regex    string
		priority int
		desc     string
	}{
		// 高优先级：完整的复合规格（如"8cmx50支/盒"）
		{`\d+\.?\d*\s*(?:cm|mm|m)\s*[xX*×]\s*\d+\.?\d*\s*(?:片|粒|支|个|只|袋|包|盒)(?:/(?:盒|包|袋|瓶|支))?`, 10, "复合尺寸规格"},

		// 高优先级：数量+单位+包装（如"50支/盒"、"30片/瓶"）
		{`\d+\.?\d*\s*(?:片|粒|支|个|只|袋|包|瓶|盒|套|条|贴|枚)\s*/\s*(?:盒|包|袋|瓶|支|罐|筒|套)`, 9, "数量包装规格"},

		// 高优先级：容量+包装（如"450ml/瓶"、"100g/支"）
		{`\d+\.?\d*\s*(?:ml|毫升|g|克|mg|毫克|μg|微克|l|升|kg|公斤)\s*/\s*(?:瓶|支|盒|包|袋|罐|筒|套)`, 9, "容量包装规格"},

		// 最高优先级：分离式数量规格（如"540一次性滚针"中的"540针"）
		{`(\d+)(?:[^0-9]*?滚?)(针|层|型|号|度)`, 11, "分离式医疗规格"},

		// 最高优先级：数量规格（如"30粒"、"20支"、"540针"）- 提高优先级
		{`\d+\.?\d*\s*(?:片|粒|支|个|只|袋|包|瓶|盒|套|条|贴|枚|罐|筒|针)`, 10, "数量规格"},

		// 高优先级：纯容量规格（如"450ml"、"100g"、"30mI"）
		{`\d+\.?\d*\s*(?:ml|mI|毫升|g|克|mg|毫克|μg|微克|l|升|kg|公斤)`, 8, "容量规格"},

		// 高优先级：特殊医疗规格（如"540针"、"15cm"）
		{`\d+\.?\d*\s*(?:针|层|型|号|度)`, 9, "医疗规格"},

		// 中高优先级：人份规格（如"单人份/盒"、"5人份"）
		{`(?:单人份|一人份|\d+人份)(?:/(?:盒|包|袋|瓶|支|罐|筒|套))?`, 9, "人份规格"},

		// 中优先级：尺寸规格（如"10x15cm"、"8cmx50"）
		{`\d+\.?\d*\s*(?:cm|mm|m)?\s*[xX*×]\s*\d+\.?\d*\s*(?:cm|mm|m)?`, 7, "尺寸规格"},

		// 中优先级：百分比规格（如"5%"、"75%"）
		{`\d+\.?\d*\s*%`, 6, "百分比规格"},

		// 低优先级：文字规格
		{`均码|标准装|常规装|大号|中号|小号|加大|加厚|多规格可选|迷你装|旅行装|家庭装|经济装|超值装`, 5, "文字规格"},

		// 最低优先级：纯数字+中文（如"第6感"）
		{`\d+[\p{Han}]+`, 3, "数字中文"},
	}

	// 收集所有匹配项
	type SpecMatch struct {
		text     string
		priority int
		desc     string
	}

	var matches []SpecMatch

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern.regex)

		// 特殊处理分离式医疗规格
		if pattern.desc == "分离式医疗规格" {
			submatches := re.FindAllStringSubmatch(cleanName, -1)
			for _, submatch := range submatches {
				if len(submatch) >= 3 {
					// 组合数字和单位，如 "540" + "针" = "540针"
					combined := submatch[1] + submatch[2]
					matches = append(matches, SpecMatch{
						text:     combined,
						priority: pattern.priority,
						desc:     pattern.desc,
					})
				}
			}
		} else {
			found := re.FindAllString(cleanName, -1)
			for _, match := range found {
				trimmed := strings.TrimSpace(match)
				if trimmed != "" {
					matches = append(matches, SpecMatch{
						text:     trimmed,
						priority: pattern.priority,
						desc:     pattern.desc,
					})
				}
			}
		}
	}

	// 获取配置
	maxLen := 30 // 增加最大长度限制
	if cfg := config.GetConfig(); cfg != nil && cfg.TagCleaning.MaxSpecLength > 0 {
		maxLen = cfg.TagCleaning.MaxSpecLength
	}

	// 过滤无效规格并评分
	var validMatches []SpecMatch
	for _, match := range matches {
		runeCount := utf8.RuneCountInString(match.text)

		// 基本长度检查
		if runeCount > maxLen {
			continue
		}

		// 检查是否为无效规格
		if isInvalidSpec(match.text) {
			continue
		}

		// 计算质量评分
		qualityScore := scoreSpecification(match.text)
		if qualityScore < 3 { // 最低质量阈值
			continue
		}

		// 添加质量评分到匹配项
		match.priority += qualityScore // 将质量评分加到优先级中
		validMatches = append(validMatches, match)
	}

	// 按综合评分选择最佳规格
	var bestMatch SpecMatch
	for _, match := range validMatches {
		if bestMatch.text == "" ||
			match.priority > bestMatch.priority ||
			(match.priority == bestMatch.priority && len(match.text) > len(bestMatch.text)) {
			bestMatch = match
		}
	}

	if bestMatch.text != "" {
		// 标准化规格
		standardized := StandardizeSpec(bestMatch.text)

		// 最终验证：确保标准化后的规格仍然有效
		if !isInvalidSpec(standardized) && scoreSpecification(standardized) >= 3 {
			return standardized
		}
	}
	return ""
}

// standardizeSpec 标准化规格单位
func standardizeSpec(spec string) string {
	// 单位标准化映射
	unitMappings := map[string]string{
		"mI":  "ml",
		"毫升":  "ml",
		"公毫升": "ml",
		"克":   "g",
		"公克":  "g",
		"毫克":  "mg",
		"公毫克": "mg",
		"微克":  "μg",
		"公微克": "μg",
		"公斤":  "kg",
		"千克":  "kg",
		"升":   "l",
		"公升":  "l",
		"L":   "l",
		"厘米":  "cm",
		"公分":  "cm",
		"毫米":  "mm",
		"公厘":  "mm",
		"米":   "m",
		"公尺":  "m",
	}

	result := spec
	for old, new := range unitMappings {
		result = strings.ReplaceAll(result, old, new)
	}

	return result
}

// ExtractSpec 规格提取函数的别名
func ExtractSpec(name string) string {
	spec := ExtractSpecification(name)
	return standardizeSpec(spec)
}

// 规格标准化
func StandardizeSpec(spec string) string {
	// 扩展的单位映射表
	unitMap := map[string]string{
		// 数量单位标准化
		"件": "个", "套": "套", "袋": "袋", "包": "袋",
		"盒": "盒", "筒": "筒", "支": "支", "瓶": "瓶",
		"个": "个", "只": "只", "条": "条", "贴": "贴",
		"枚": "枚", "罐": "罐", "片": "片", "粒": "粒",

		// 重量单位标准化
		"g": "g", "克": "g", "公克": "g",
		"mg": "mg", "毫克": "mg", "公毫克": "mg",
		"μg": "μg", "微克": "μg", "公微克": "μg",
		"kg": "kg", "公斤": "kg", "千克": "kg",

		// 容量单位标准化
		"ml": "ml", "毫升": "ml", "公毫升": "ml",
		"l": "l", "升": "l", "公升": "l",
		"L": "l", // 大写L转小写

		// 长度单位标准化
		"cm": "cm", "厘米": "cm", "公分": "cm",
		"mm": "mm", "毫米": "mm", "公厘": "mm",
		"m": "m", "米": "m", "公尺": "m",
	}

	// 清理和标准化
	result := strings.TrimSpace(spec)

	// 标准化分隔符
	result = strings.ReplaceAll(result, "／", "/")
	result = strings.ReplaceAll(result, "×", "x")
	result = strings.ReplaceAll(result, "*", "x")

	// 标准化人份表达
	personPatterns := map[string]string{
		"单人份": "1人份",
		"一人份": "1人份",
		"二人份": "2人份",
		"三人份": "3人份",
		"四人份": "4人份",
		"五人份": "5人份",
		"六人份": "6人份",
		"七人份": "7人份",
		"八人份": "8人份",
		"九人份": "9人份",
		"十人份": "10人份",
		"人/份": "人份",
	}

	for old, new := range personPatterns {
		result = strings.ReplaceAll(result, old, new)
	}

	// 应用单位映射
	for old, new := range unitMap {
		result = strings.ReplaceAll(result, old, new)
	}

	// 标准化尺寸表达（统一使用小写x）
	re := regexp.MustCompile(`(\d+\.?\d*)\s*[X*×]\s*(\d+\.?\d*)`)
	result = re.ReplaceAllString(result, "${1}x${2}")

	// 标准化包装表达
	result = strings.ReplaceAll(result, "/盒", "/盒")
	result = strings.ReplaceAll(result, "/瓶", "/瓶")
	result = strings.ReplaceAll(result, "/袋", "/袋")
	result = strings.ReplaceAll(result, "/包", "/包")

	// 清理多余空格
	spaceRe := regexp.MustCompile(`\s+`)
	result = spaceRe.ReplaceAllString(result, " ")

	// 移除首尾的特殊字符
	result = strings.Trim(result, " \t\n\r-_()[]【】（）")

	return strings.TrimSpace(result)
}

// 验证规格提取质量
func ValidateSpecExtraction(productName, extractedSpec string) (bool, string) {
	if extractedSpec == "" {
		return false, "未提取到规格"
	}

	// 检查是否包含有效的规格信息
	validPatterns := []string{
		`\d+\.?\d*\s*(?:ml|g|mg|μg|l|kg|cm|mm|m|%)`,     // 数字+单位
		`\d+\.?\d*\s*(?:片|粒|支|个|只|袋|包|瓶|盒|套|条|贴|枚|罐|筒)`, // 数字+数量单位
		`\d+\.?\d*\s*[xX*×]\s*\d+\.?\d*`,                // 尺寸规格
		`\d*人份`,                                         // 人份规格
		`均码|标准装|常规装|大号|中号|小号|加大|加厚|迷你装|旅行装|家庭装`, // 文字规格
	}

	for _, pattern := range validPatterns {
		if matched, _ := regexp.MatchString(pattern, extractedSpec); matched {
			return true, "规格提取有效"
		}
	}

	return false, "提取的规格格式无效"
}

// 批量测试规格提取
func TestSpecExtraction() {
	testCases := []struct {
		productName string
		expected    string
		description string
	}{
		// 基础测试用例
		{"[三精]葡萄糖补水液(原味)450ml/瓶", "450ml/瓶", "容量包装规格"},
		{"[可孚] 医用棉签 碘伏棉签 便携碘伏棉棒一次性医用8cmx50支/盒", "8cmx50支/盒", "复合尺寸规格"},
		{"[京都念慈菴]枇杷糖45g(2.5g*18粒)/盒", "45g", "容量规格"},
		{"[佳禾]压力绷带乳腺术后医用加压护胸带胸衣弹力绑定胸部固定手术后IV型1个/盒（多规格）", "1个/盒", "数量包装规格"},
		{"[白云山]维生素C咀嚼片维生素甜橙味蓝帽食品保健品 0.6g*片*60片/瓶", "60片/瓶", "数量包装规格"},
		{"[妥能]医用水胶体敷料褥疮压疮贴造口伤口溃疡护理人工皮贴痘痘吸收渗液10x10cm 1片装", "10x10cm", "尺寸规格"},
		{"[祥医堂]咪奎莫特乳膏抑疣平尤乳膏外用丝状跖尤颈部脖子肉粒5% 20g 1支/盒", "5%", "百分比规格"},

		// 杂乱规格测试用例
		{"【买三送一】稻穗薄荷香筒（泰国薄荷味）-碧艾迪尔泰国企业有限公司", "", "促销信息应被过滤"},
		{"检测试剂 5人份/盒 2025年", "5人份/盒", "年份信息应被过滤"},
		{"医用敷料 24cm*15.5cm 单人份/盒", "24cmx15.5cm", "尺寸规格标准化"},
		{"SP10蛋白检测试剂（胶体金法）", "", "产品型号应被过滤"},
		{"胶原蛋白含量＞650毫克/ml", "", "复杂含量描述应被过滤"},
		{"20盒送1盒 天使装", "", "促销和包装描述应被过滤"},
		{"五人份 15g/支", "5人份", "人份规格标准化"},
		{"双条装 盒装", "", "包装描述应被过滤"},
	}

	fmt.Println("\n=== 规格提取测试 ===")
	successCount := 0

	for i, tc := range testCases {
		extracted := ExtractSpecification(tc.productName)
		isValid, msg := ValidateSpecExtraction(tc.productName, extracted)

		fmt.Printf("\n测试 %d: %s\n", i+1, tc.description)
		fmt.Printf("商品名称: %s\n", tc.productName)
		fmt.Printf("期望规格: %s\n", tc.expected)
		fmt.Printf("提取规格: %s\n", extracted)
		fmt.Printf("验证结果: %s\n", msg)

		if isValid && (extracted == tc.expected || strings.Contains(tc.productName, extracted)) {
			fmt.Printf("✓ 测试通过\n")
			successCount++
		} else {
			fmt.Printf("✗ 测试失败\n")
		}
	}

	fmt.Printf("\n=== 测试总结 ===\n")
	fmt.Printf("总测试数: %d\n", len(testCases))
	fmt.Printf("成功数: %d\n", successCount)
	fmt.Printf("成功率: %.1f%%\n", float64(successCount)/float64(len(testCases))*100)
}

// 生成清洗后的商品名称 - 关键修改点
func GenerateCleanedName(p *Product) {
	// 确保规格有效
	if p.Spec == "" {
		p.Spec = ExtractSpecification(p.ProductName)
	}

	// 处理品牌：保持AI处理的品牌结果，不再重新提取
	brand := p.Brand
	fmt.Printf("[监控-CLEAN-1] 清洗前品牌: '%s'\n", brand)
	if brand == "" {
		// 只有当品牌完全为空时，才使用简单的后备提取
		brand = "无品牌"
		fmt.Printf("[监控-CLEAN-2] 品牌为空，设置默认: '%s'\n", brand)
	} else {
		fmt.Printf("[监控-CLEAN-2] 保持原品牌: '%s'\n", brand)
	}

	coreTag := p.CoreTag
	if coreTag == "" {
		coreTag = "无核心标签"
	}

	spec := p.Spec
	if spec == "" {
		// 最终后备方案
		spec = ExtractSpecification(p.ProductName)
		if spec == "" {
			spec = "无规格"
		}
	}

	// 添加品名清洗过程日志
	fmt.Printf("[品名清洗] 原始名称: %s\n", p.ProductName)
	fmt.Printf("  - 品牌: %s\n", brand)
	fmt.Printf("  - 核心标签: %s\n", coreTag)
	fmt.Printf("  - 规格: %s\n", spec)
	fmt.Printf("  - 清洗后名称: %s_%s_%s\n", brand, coreTag, spec)

	p.CleanedName = brand + "_" + coreTag + "_" + spec
	fmt.Printf("[监控-CLEAN-3] 最终清洗后品牌: '%s'\n", brand)
}

// 安全截取字符串，避免截断中文字符
func SafeTruncate(s string, maxLen int) string {
	runes := []rune(s)
	if len(runes) <= maxLen {
		return s
	}
	return string(runes[:maxLen])
}

// 增强特征标签提取（返回多个特征标签）
func ExtractFeatureTags(name string, cfg *config.Config) []string {
	if cfg == nil {
		return nil
	}

	// 定义有效的特征词类型：材质、功能、颜色、工艺、风格、成分、感官
	featurePatterns := []string{
		"便携", "防水", "防过敏", "夜用", "日用", "透气",
		"无菌", "一次性", "可重复", "透明", "彩色",
		"免蹲", "可调节", "3D", "立体", "卡通",
		"凸点", "凸点型", "硅胶", "棉质", "金属",
		"抗菌", "防滑", "夜光", "无痕", "加厚",
		"薄款", "蕾丝", "纯棉", "网面", "冰丝",
		"薄荷", "花香", "果味", "清凉", "温热",
		"大容量", "小包装", "迷你", "旅行装", "家庭装",
	}

	name = strings.ToLower(name)
	var tags []string

	// 匹配所有特征词
	for _, pattern := range featurePatterns {
		if strings.Contains(name, pattern) {
			normalized := mapping.NormalizeFeatureTag(pattern)
			// 避免重复添加
			if !containsString(tags, normalized) {
				tags = append(tags, normalized)
			}
		}
	}

	// 尝试匹配规格中的特征词
	spec := ExtractSpecification(name)
	if spec != "" {
		spec = strings.ToLower(spec)
		for _, pattern := range featurePatterns {
			if strings.Contains(spec, pattern) {
				normalized := mapping.NormalizeFeatureTag(pattern)
				if !containsString(tags, normalized) {
					tags = append(tags, normalized)
				}
			}
		}
	}

	return tags
}

// 添加相似核心标签查询
func FindSimilarCoreTags(coreTag string, threshold float64) []string {
	var tags []string
	query := `
        SELECT core_tag FROM products 
        WHERE core_tag != '' AND length(core_tag) > 1
        GROUP BY core_tag
        HAVING MAX(similarity(core_tag, ?)) > ?
        ORDER BY COUNT(*) DESC
        LIMIT 3
    `
	rows, err := DB.Query(query, coreTag, threshold)
	if err != nil {
		return nil
	}
	defer rows.Close()

	for rows.Next() {
		var tag string
		if err := rows.Scan(&tag); err == nil {
			tags = append(tags, tag)
		}
	}
	return tags
}

// ExtractBrand 从商品名称中提取品牌
func ExtractBrand(name string, noiseWords []string) string {
	// 1. 移除常见干扰词
	for _, word := range noiseWords {
		name = strings.ReplaceAll(name, word, "")
	}

	// 2. 医疗产品常见前缀，不应作为品牌
	medicalPrefixes := []string{
		"医用", "无菌", "一次性", "灭菌", "消毒", "外科", "临床", "家用",
		"专业", "高级", "标准", "进口", "国产", "正品", "原装",
	}

	// 3. 提取可能品牌名 (中文+字母数字组合)
	re := regexp.MustCompile(`[\p{Han}]{2,6}|[a-zA-Z]{3,10}`)
	matches := re.FindAllString(name, -1)

	if len(matches) > 0 {
		// 跳过医疗前缀，寻找真正的品牌
		for _, candidate := range matches {
			// 检查是否为医疗前缀
			isMedicalPrefix := false
			for _, prefix := range medicalPrefixes {
				if candidate == prefix {
					isMedicalPrefix = true
					break
				}
			}

			// 如果不是医疗前缀，返回作为品牌候选
			if !isMedicalPrefix {
				return candidate
			}
		}
	}

	return "无品牌"
}

// 修改品牌清洗函数
func CleanBrandName(brand string) string {
	if brand == "" {
		return "无品牌"
	}

	cfg := config.GetConfig()
	if cfg != nil {
		// 移除干扰词
		for _, word := range cfg.TagCleaning.BrandNoiseWords {
			brand = strings.ReplaceAll(brand, word, "")
		}
	}

	// 提取核心品牌词，保留完整品牌名
	re := regexp.MustCompile(`[\p{Han}a-zA-Z0-9&\-]+`)
	matches := re.FindAllString(brand, -1)
	if len(matches) > 0 {
		// 保留完整品牌名，不截断
		return strings.Join(matches, " ")
	}

	return "无品牌"
}

// file: database.go
func UpsertProduct(p *Product) error {
	// === 🔍 数据库存储监控 ===
	log.Printf("开始Upsert商品: %s", p.ProductName)
	log.Printf("[DB-监控-1] 存储前品牌: '%s'", p.Brand)
	log.Printf("[DB-监控-2] 存储前核心标签: '%s'", p.CoreTag)
	log.Printf("[DB-监控-3] 存储前场景: '%s'", p.Scene)
	log.Printf("[DB-监控-4] 存储前人群: '%s'", p.Crowd)

	// 检查是否已存在相同记录
	exists, err := ProductExists(p.ProductName, p.Category2)
	if err != nil {
		log.Printf("检查商品存在性失败: %v", err)
		return err
	}

	// 调用结构体自身的方法
	p.AutoMapCategories()

	// 确保特征标签被正确提取
	if len(p.FeatureTags) == 0 {
		cfg := config.GetConfig()
		p.FeatureTags = ExtractFeatureTags(p.ProductName, cfg)
	}

	GenerateCleanedName(p)

	p.Category2 = mapping.FilterCategories(p.Crowd, p.Category2)
	if len(p.Category2) > 0 && len(p.Category1) == 0 {
		p.Category1 = mapping.GetCategories1(p.Category2)
	}

	// 根据是否存在决定操作类型
	var result sql.Result
	if exists {
		log.Printf("商品已存在，执行更新: %s - %v", p.ProductName, p.Category2)
		result, err = DB.Exec(`
            UPDATE products SET
                core_tag = ?,
                scene = ?,
                crowd = ?,
                brand = ?,
                spec = ?,
                category1 = ?,
                category2 = ?,
                cleaned_name = ?,
                feature_tags = ?
            WHERE product_name = ? AND category2 = ?
        `,
			p.CoreTag,
			p.Scene,
			p.Crowd,
			p.Brand,
			p.Spec,
			strings.Join(p.Category1, ","),
			strings.Join(p.Category2, ","),
			p.CleanedName,
			strings.Join(p.FeatureTags, ","),
			p.ProductName,
			strings.Join(p.Category2, ","))
	} else {
		log.Printf("商品不存在，执行插入: %s - %v", p.ProductName, p.Category2)
		log.Printf("[DB-监控-5] 插入前品牌确认: '%s'", p.Brand)
		result, err = DB.Exec(`
            INSERT INTO products (
                product_name, core_tag, scene, crowd, brand,
                spec, category1, category2, cleaned_name, feature_tags
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
			p.ProductName,
			p.CoreTag,
			p.Scene,
			p.Crowd,
			p.Brand,
			p.Spec,
			strings.Join(p.Category1, ","),
			strings.Join(p.Category2, ","),
			p.CleanedName,
			strings.Join(p.FeatureTags, ","))
		log.Printf("[DB-监控-6] 插入执行完成，错误: %v", err)
	}

	if err != nil {
		// 处理唯一约束冲突错误（并发情况下的保护）
		if strings.Contains(err.Error(), "UNIQUE constraint failed") {
			log.Printf("唯一约束冲突: %s - %v", p.ProductName, p.Category2)
			// 递归调用自身处理冲突
			return UpsertProduct(p)
		}
		log.Printf("Upsert商品失败: %s, 错误: %v", p.ProductName, err)
		return err
	}

	// === 🔧 数据库保存后品牌验证 ===
	log.Printf("[DB-品牌验证-1] 数据库保存完成，开始验证品牌数据")
	log.Printf("[DB-品牌验证-2] 保存的品牌: '%s'", p.Brand)

	// 立即查询验证数据是否正确保存
	var savedBrand string
	queryErr := DB.QueryRow("SELECT brand FROM products WHERE product_name = ? ORDER BY id DESC LIMIT 1", p.ProductName).Scan(&savedBrand)
	if queryErr != nil {
		log.Printf("[DB-品牌验证-3] ⚠️ 查询保存的品牌失败: %v", queryErr)
	} else {
		log.Printf("[DB-品牌验证-4] 数据库中的品牌: '%s'", savedBrand)
		if savedBrand != p.Brand {
			log.Printf("[DB-品牌验证-5] ⚠️ 品牌数据不一致！期望: '%s', 实际: '%s'", p.Brand, savedBrand)
		} else {
			log.Printf("[DB-品牌验证-6] ✅ 品牌数据一致性验证通过")
		}
	}

	// 获取影响行数
	rowsAffected, _ := result.RowsAffected()
	log.Printf("Upsert成功: %s, 影响行数: %d", p.ProductName, rowsAffected)

	return nil
}

// GetProductsWithMissingBrands 获取品牌缺失的商品
func GetProductsWithMissingBrands(limit int) ([]Product, error) {
	query := `
		SELECT id, product_name, core_tag, scene, crowd, brand, spec, category1, category2,
		       cleaned_name, feature_tags, created_at, updated_at
		FROM products
		WHERE brand IS NULL OR brand = '' OR brand = '无品牌'
		LIMIT ?
	`

	rows, err := DB.Query(query, limit)
	if err != nil {
		return nil, fmt.Errorf("查询品牌缺失商品失败: %v", err)
	}
	defer rows.Close()

	var products []Product
	for rows.Next() {
		var product Product
		var category1Str, category2Str, featureTagsStr sql.NullString
		var createdAt, updatedAt sql.NullString

		err := rows.Scan(
			&product.ID,
			&product.ProductName,
			&product.CoreTag,
			&product.Scene,
			&product.Crowd,
			&product.Brand,
			&product.Spec,
			&category1Str,
			&category2Str,
			&product.CleanedName,
			&featureTagsStr,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描商品数据失败: %v", err)
		}

		// 解析category1
		if category1Str.Valid && category1Str.String != "" {
			product.Category1 = strings.Split(category1Str.String, ",")
			for i, cat := range product.Category1 {
				product.Category1[i] = strings.TrimSpace(cat)
			}
		}

		// 解析category2
		if category2Str.Valid && category2Str.String != "" {
			product.Category2 = strings.Split(category2Str.String, ",")
			for i, cat := range product.Category2 {
				product.Category2[i] = strings.TrimSpace(cat)
			}
		}

		// 解析feature_tags
		if featureTagsStr.Valid && featureTagsStr.String != "" {
			product.FeatureTags = strings.Split(featureTagsStr.String, ",")
			for i, tag := range product.FeatureTags {
				product.FeatureTags[i] = strings.TrimSpace(tag)
			}
		}

		products = append(products, product)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历查询结果失败: %v", err)
	}

	return products, nil
}

// UpdateProductBrand 更新商品的品牌信息
func UpdateProductBrand(productID int, brand string) error {
	query := `UPDATE products SET brand = ? WHERE id = ?`

	_, err := DB.Exec(query, brand, productID)
	if err != nil {
		return fmt.Errorf("更新商品品牌失败: %v", err)
	}

	return nil
}
