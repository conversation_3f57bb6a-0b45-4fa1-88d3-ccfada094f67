package utils

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"unicode/utf8"
)

// 将JSON数据写入文件，格式化输出
func WriteJSONToFile(data interface{}, filePath string) error {
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 格式化JSON
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("JSON编码失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(filePath, jsonData, 0644); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	return nil
}

// 读取JSON文件
func ReadJSONFromFile(filePath string, v interface{}) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return fmt.Errorf("读取文件失败: %w", err)
	}

	if err := json.Unmarshal(data, v); err != nil {
		return fmt.Errorf("解析JSON失败: %w", err)
	}

	return nil
}

// 将字符串中的中文括号转换为英文括号
func ConvertChineseParens(s string) string {
	s = strings.ReplaceAll(s, "（", "(")
	s = strings.ReplaceAll(s, "）", ")")
	return s
}

// 移除字符串中的中文括号内容
func RemoveChineseParensContent(s string) string {
	s = strings.ReplaceAll(s, "（", "(")
	s = strings.ReplaceAll(s, "）", ")")
	return RemoveParensContent(s)
}

// 移除字符串中的括号内容
func RemoveParensContent(s string) string {
	var result bytes.Buffer
	openCount := 0

	for _, r := range s {
		switch r {
		case '(':
			openCount++
		case ')':
			if openCount > 0 {
				openCount--
			}
		default:
			if openCount == 0 {
				result.WriteRune(r)
			}
		}
	}

	return result.String()
}

// GetInput 获取用户输入
func GetInput(prompt string) string {
	fmt.Print(prompt)

	// 使用 bufio.Scanner 替代 fmt.Scanln 以避免输入问题
	scanner := bufio.NewScanner(os.Stdin)
	if scanner.Scan() {
		return strings.TrimSpace(scanner.Text())
	}

	// 如果扫描失败，返回空字符串
	if err := scanner.Err(); err != nil {
		fmt.Printf("输入读取错误: %v\n", err)
	}
	return ""
}

// ConfirmAction 确认操作
func ConfirmAction() bool {
	maxAttempts := 5 // 最大尝试次数，避免无限循环
	attempts := 0

	for attempts < maxAttempts {
		attempts++
		input := GetInput("> 请输入选择 (y/n): ")

		// 如果输入为空且尝试次数过多，默认返回 false
		if input == "" {
			if attempts >= 3 {
				fmt.Println("! 多次输入为空，默认选择 'n' (取消操作)")
				return false
			}
			fmt.Println("! 输入为空，请重新输入")
			continue
		}

		switch strings.ToLower(input) {
		case "y", "yes", "是":
			return true
		case "n", "no", "否":
			return false
		default:
			fmt.Printf("! 输入无效: '%s'，请输入 y/n 或 是/否\n", input)
		}
	}

	// 达到最大尝试次数，默认返回 false
	fmt.Printf("! 已达到最大尝试次数 (%d)，默认选择 'n' (取消操作)\n", maxAttempts)
	return false
}

// GetIntInput 获取整数输入
func GetIntInput(prompt string, min, max int) int {
	for {
		input := GetInput(prompt)
		var choice int
		if _, err := fmt.Sscanf(input, "%d", &choice); err == nil {
			if choice >= min && choice <= max {
				return choice
			}
		}
		fmt.Printf("! 输入无效，请输入 %d 到 %d 之间的数字\n", min, max)
	}
}

// IsValidCoreTag 验证核心标签是否有效
func IsValidCoreTag(tag string) bool {
	charCount := utf8.RuneCountInString(tag)
	return charCount >= 2 && charCount <= 4
}

// PrintSectionHeader 打印部分标题
func PrintSectionHeader(title string) {
	fmt.Printf("\n%s\n", strings.Repeat("=", 60))
	fmt.Printf("  %s\n", title)
	fmt.Println(strings.Repeat("-", 60))
}

// GetInputWithNavigation 带导航提示的输入函数
func GetInputWithNavigation(prompt string, currentStep, totalSteps int) string {
	fmt.Printf("[步骤 %d/%d] %s (输入0返回上一步): ", currentStep, totalSteps, prompt)
	return GetInput("")
}

// GetIntInputWithNavigation 带导航提示的整数输入
func GetIntInputWithNavigation(prompt string, min, max, currentStep, totalSteps int) int {
	fmt.Printf("[步骤 %d/%d] %s (输入0返回上一步): ", currentStep, totalSteps, prompt)

	for {
		input := GetInput("")
		if input == "0" {
			return 0
		}

		var value int
		if _, err := fmt.Sscanf(input, "%d", &value); err == nil {
			if value >= min && value <= max {
				return value
			}
		}
		fmt.Printf("! 输入无效，请输入 %d 到 %d 之间的数字\n", min, max)
	}
}

// PrintMenu 打印菜单
func PrintMenu(title string, options []string) {
	fmt.Printf("\n%s\n", strings.Repeat("=", 60))
	fmt.Printf("  %s\n", title)
	fmt.Println(strings.Repeat("-", 60))
	for i, option := range options {
		fmt.Printf("%d. %s\n", i+1, option)
	}
	fmt.Println(strings.Repeat("=", 60))
}

// GetChoice 获取选择
func GetChoice(min, max int) int {
	return GetIntInput("> 请输入选择: ", min, max)
}

// ShowManualReview 显示手动审核
func ShowManualReview(productName, originalTag string, options []string) int {
	// 打印手动审核信息
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Printf("产品: %s\n原始标签: %s\n", productName, originalTag)
	fmt.Println("选项:")
	for i, option := range options {
		fmt.Printf("%d. %s\n", i+1, option)
	}
	fmt.Println(strings.Repeat("=", 60))

	// 获取用户选择
	choice := GetIntInput("> 请选择标签: ", 1, len(options))
	return choice
}

// GetTimestamp 获取时间戳
func GetTimestamp() string {
	return "00000000_000000" // 示例值
}

// PrintMenuWithNavigation 带导航提示的选择菜单
func PrintMenuWithNavigation(title string, options []string, currentStep, totalSteps int) {
	fmt.Printf("\n[步骤 %d/%d] %s\n", currentStep, totalSteps, strings.Repeat("=", 40))
	fmt.Printf("  %s\n", title)
	fmt.Println(strings.Repeat("-", 40))
	for i, option := range options {
		fmt.Printf("%d. %s\n", i+1, option)
	}
	fmt.Println(strings.Repeat("=", 40))
}

// EnsureUTF8 确保字符串是有效的UTF-8编码
func EnsureUTF8(s string) string {
	// === 🔍 UTF8处理监控 ===
	fmt.Printf("[UTF8-监控-1] 输入字符串: '%s'\n", s)
	fmt.Printf("[UTF8-监控-2] UTF8有效性检查: %v\n", utf8.ValidString(s))

	if !utf8.ValidString(s) {
		fmt.Printf("[UTF8-监控-3] ⚠️ 字符串包含无效UTF-8字符: %q\n", s)

		// 创建有效字符的缓冲区
		var buf bytes.Buffer

		// 遍历每个rune，跳过无效字符
		for i, r := range s {
			if r == utf8.RuneError {
				// 尝试解码单个字节
				_, size := utf8.DecodeRuneInString(s[i:])
				if size == 1 {
					// 跳过无效字节
					fmt.Printf("[UTF8-监控-4] 跳过无效字节在位置 %d\n", i)
					continue
				}
			}
			buf.WriteRune(r)
		}
		s = buf.String()
		fmt.Printf("[UTF8-监控-5] 清理后字符串: '%s'\n", s)

		// 如果处理后为空，使用默认值
		if s == "" {
			s = "无品牌"
			fmt.Printf("[UTF8-监控-6] 字符串为空，设置默认值: '%s'\n", s)
		}
	} else {
		fmt.Printf("[UTF8-监控-3] ✅ 字符串UTF8有效，直接返回: '%s'\n", s)
	}

	fmt.Printf("[UTF8-监控-7] 最终返回: '%s'\n", s)
	return s
}
