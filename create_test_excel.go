//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"

	"github.com/xuri/excelize/v2"
)

func main() {
	// 创建新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 设置工作表名称
	sheetName := "Sheet1"

	// 设置表头
	f.SetCellValue(sheetName, "A1", "商品名称")

	// 添加测试商品
	testProducts := []string{
		"稳健医疗一次性医用外科口罩50只装成人防护三层熔喷布医用口罩",
		"海氏海诺75%酒精湿巾杀菌消毒湿纸巾便携装10片*20包",
		"鱼跃yuwell血压计家用上臂式全自动高精准电子血压测量仪YE666AR",
		"欧姆龙OMRON电子血压计家用上臂式全自动血压测量仪HEM-7136",
		"三诺安稳血糖试纸50片装+采血针50支血糖仪试纸条",
	}

	// 填充数据
	for i, product := range testProducts {
		cell := fmt.Sprintf("A%d", i+2)
		f.SetCellValue(sheetName, cell, product)
	}

	// 保存文件
	filename := "test_products.xlsx"
	if err := f.SaveAs(filename); err != nil {
		log.Fatalf("保存Excel文件失败: %v", err)
	}

	fmt.Printf("✅ 测试Excel文件已创建: %s\n", filename)
	fmt.Printf("📊 包含 %d 个测试商品\n", len(testProducts))
}
