package shared

import (
	"fmt"
	"regexp"
	"runtime"
	"sort"
	"strings"
	"sync"

	"github.com/mogan/tag-assistant/config"
	"github.com/mogan/tag-assistant/db"
)

// ======= 接口和结构体定义 =======

// 定义产品接口
type Product interface {
	GetProductName() string
	GetCoreTag() string
	GetScene() string
	GetCrowd() string
	GetBrand() string
	GetSpec() string
	GetCategory1() []string
	GetCategory2() []string
	GetCleanedName() string
	GetFeatureTags() []string // 添加新方法
}

// 定义内存中的产品结构
type MemoryProduct struct {
	ProductName string
	CoreTag     string
	Scene       string
	Crowd       string
	Brand       string
	Spec        string
	Category1   []string
	Category2   []string
	CleanedName string
	FeatureTags []string // 添加特征标签字段
}

// 实现产品接口的方法
func (mp MemoryProduct) GetProductName() string   { return mp.ProductName }
func (mp MemoryProduct) GetCoreTag() string       { return mp.CoreTag }
func (mp MemoryProduct) GetScene() string         { return mp.Scene }
func (mp MemoryProduct) GetCrowd() string         { return mp.Crowd }
func (mp MemoryProduct) GetBrand() string         { return mp.Brand }
func (mp MemoryProduct) GetSpec() string          { return mp.Spec }
func (mp MemoryProduct) GetCategory1() []string   { return mp.Category1 }
func (mp MemoryProduct) GetCategory2() []string   { return mp.Category2 }
func (mp MemoryProduct) GetCleanedName() string   { return mp.CleanedName }
func (mp MemoryProduct) GetFeatureTags() []string { return mp.FeatureTags } // 实现新方法

// ======= Memory 结构体定义 =======

type Memory struct {
	productMap   map[string]Product
	coreTagMap   map[string][]string
	categoryMap  map[string][]string
	similarityDB map[string]string
	fetcher      DBProductFetcher
	mutex        sync.RWMutex
}

var GlobalMemory *Memory

func init() {
	GlobalMemory = &Memory{
		productMap:   make(map[string]Product),
		coreTagMap:   make(map[string][]string),
		categoryMap:  make(map[string][]string),
		similarityDB: make(map[string]string),
	}
	fmt.Println("共享记忆系统已初始化")
}

// ======= Memory 方法实现 =======

// 添加SetFetcher方法
func (sm *Memory) SetFetcher(f DBProductFetcher) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	sm.fetcher = f
}

func (sm *Memory) AddProduct(p Product) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	// 确保数据结构已初始化
	if sm.productMap == nil {
		sm.productMap = make(map[string]Product)
	}
	if sm.coreTagMap == nil {
		sm.coreTagMap = make(map[string][]string)
	}
	if sm.categoryMap == nil {
		sm.categoryMap = make(map[string][]string)
	}
	if sm.similarityDB == nil {
		sm.similarityDB = make(map[string]string)
	}

	// 检查产品名称是否为空
	if p.GetProductName() == "" {
		fmt.Println("! 警告: 尝试添加产品名称为空的产品")
		return
	}

	// 使用标准化名称作为key
	normalized := NormalizeProductName(p.GetProductName())

	// 直接存储产品，不再进行内部转换（假设外部已经处理好）
	sm.productMap[normalized] = p

	// 更新核心标签映射
	if p.GetCoreTag() != "" {
		sm.coreTagMap[p.GetCoreTag()] = append(
			sm.coreTagMap[p.GetCoreTag()],
			normalized, // 使用标准化名称
		)
	}

	// 更新类目映射
	if len(p.GetCategory2()) > 0 {
		for _, cat := range p.GetCategory2() {
			sm.categoryMap[cat] = append(
				sm.categoryMap[cat],
				normalized, // 使用标准化名称
			)
		}
	}

	// 更新相似度数据库
	sm.similarityDB[normalized] = p.GetCoreTag()

	// 内存优化：定期清理
	if len(sm.productMap)%1000 == 0 {
		sm.optimizeMemory()
	}
}

// 新增内存优化方法
func (sm *Memory) optimizeMemory() {
	fmt.Printf("执行内存优化 (当前产品数: %d)\n", len(sm.productMap))

	// 1. 清理无效条目
	for key, p := range sm.productMap {
		if p.GetProductName() == "" || p.GetCoreTag() == "" {
			delete(sm.productMap, key)
			fmt.Printf("清理无效产品: %s\n", key)
		}
	}

	// 2. 重建索引
	sm.coreTagMap = make(map[string][]string)
	sm.categoryMap = make(map[string][]string)

	for _, p := range sm.productMap {
		normalized := NormalizeProductName(p.GetProductName())

		// 更新核心标签映射
		if p.GetCoreTag() != "" {
			sm.coreTagMap[p.GetCoreTag()] = append(
				sm.coreTagMap[p.GetCoreTag()],
				normalized,
			)
		}

		// 更新类目映射
		if len(p.GetCategory2()) > 0 {
			for _, cat := range p.GetCategory2() {
				sm.categoryMap[cat] = append(
					sm.categoryMap[cat],
					normalized,
				)
			}
		}
	}

	// 3. 手动触发GC
	runtime.GC()
	fmt.Printf("内存优化完成 (剩余产品数: %d)\n", len(sm.productMap))
}

func (sm *Memory) GetProductByName(name string) (Product, bool) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	// 关键修复：对输入名称进行标准化
	normalized := NormalizeProductName(name)
	p, exists := sm.productMap[normalized]
	return p, exists
}

func (sm *Memory) GetProductsByCoreTag(tag string) []Product {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	var products []Product
	if names, exists := sm.coreTagMap[tag]; exists {
		for _, name := range names {
			if p, ok := sm.productMap[name]; ok {
				products = append(products, p)
			}
		}
	}
	return products
}

func (sm *Memory) FindSimilarTag(name string) (string, bool) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	normalized := NormalizeProductName(name)

	// 1. 首先尝试精确匹配
	if tag, exists := sm.similarityDB[normalized]; exists {
		return tag, true
	}

	// 2. 如果精确匹配失败，进行相似度匹配
	bestTag := ""
	bestSimilarity := 0.0
	threshold := 0.6 // 降低相似度阈值

	fmt.Printf("[相似度匹配] 开始查找相似商品，当前商品: %s\n", normalized)
	fmt.Printf("[相似度匹配] 数据库中有 %d 个商品\n", len(sm.similarityDB))

	// 遍历所有已知的商品，进行相似度计算
	for existingName, existingTag := range sm.similarityDB {
		similarity := calculateEnhancedSimilarity(normalized, existingName)
		fmt.Printf("[相似度匹配] %s vs %s = %.3f (标签: %s)\n", normalized, existingName, similarity, existingTag)

		if similarity >= threshold && similarity > bestSimilarity {
			bestSimilarity = similarity
			bestTag = existingTag
			fmt.Printf("[相似度匹配] 找到更好的匹配: %.3f -> %s\n", similarity, bestTag)
		}
	}

	fmt.Printf("[相似度匹配] 最终结果: 最佳相似度=%.3f, 最佳标签=%s\n", bestSimilarity, bestTag)

	// 如果找到相似的商品，返回其标签
	if bestTag != "" {
		// 将这个映射加入缓存，避免重复计算
		sm.similarityDB[normalized] = bestTag
		return bestTag, true
	}

	return "", false
}

// 增强相似度计算函数（基于关键词提取）
func calculateEnhancedSimilarity(text1, text2 string) float64 {
	if text1 == text2 {
		return 1.0
	}

	// 1. 提取核心关键词（去除噪音词）
	coreKeywords1 := extractCoreKeywords(text1)
	coreKeywords2 := extractCoreKeywords(text2)

	fmt.Printf("[关键词提取] %s -> %v\n", text1, coreKeywords1)
	fmt.Printf("[关键词提取] %s -> %v\n", text2, coreKeywords2)

	// 2. 如果没有提取到关键词，返回低相似度
	if len(coreKeywords1) == 0 || len(coreKeywords2) == 0 {
		return 0.0
	}

	// 3. 计算关键词相似度
	keywordSimilarity := calculateKeywordSimilarity(coreKeywords1, coreKeywords2)

	// 4. 特殊规则增强
	if hasSpecialSimilarKeywords(coreKeywords1, coreKeywords2) {
		keywordSimilarity = 0.95
	}

	fmt.Printf("[关键词相似度] %.3f\n", keywordSimilarity)

	return keywordSimilarity
}

// 提取核心关键词（去除噪音词）
func extractCoreKeywords(text string) []string {
	// 1. 移除常见噪音词
	cleanedText := removeNoiseWords(text)

	// 2. 提取医疗用品核心关键词
	coreKeywords := []string{
		"生理盐水", "生理性盐水", "氯化钠", "盐水", "注射液",
		"精华液", "精华", "原液", "精华素", "精华水",
		"疤痕贴", "瘢痕贴", "疤痕敷贴", "瘢痕敷贴", "疤痕修复贴",
		"坐便椅", "坐便器", "坐便凳", "大便椅", "厕所椅", "马桶椅",
		"医用棉签", "棉棒", "棉签", "碘伏棉棒", "碘伏棉签",
		"创可贴", "创口贴", "止血贴", "创伤贴",
		"消毒液", "消毒剂", "酒精", "碘伏",
		"口罩", "防护口罩", "医用口罩", "N95",
		"纱布", "医用纱布", "无菌纱布",
		"绷带", "弹性绷带", "医用绷带",
	}

	var found []string
	for _, keyword := range coreKeywords {
		if strings.Contains(cleanedText, keyword) {
			found = append(found, keyword)
		}
	}

	return found
}

// 移除噪音词
func removeNoiseWords(text string) string {
	// 噪音词列表（移除品牌名，避免影响品牌识别）
	noiseWords := []string{
		// 规格相关
		"ml", "支", "盒", "瓶", "袋", "包", "个", "片", "粒", "罐", "筒",
		"cm", "mm", "g", "mg", "kg", "%",
		// 用途描述
		"洗鼻", "洗眼", "纹绣", "专用", "清洁", "微针", "控油", "去痘",
		"保湿", "滋润", "抗皱", "补水", "亮肤", "细致", "毛孔",
		// 修饰词
		"医用", "一次性", "无菌", "透明", "便携", "家用",
		// 数字和符号
		"0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
		".", "_", "-", "+", "×", "*", "/",
	}

	cleaned := text
	for _, noise := range noiseWords {
		cleaned = strings.ReplaceAll(cleaned, noise, " ")
	}

	// 清理多余空格
	cleaned = strings.Join(strings.Fields(cleaned), " ")

	return cleaned
}

// 关键词相似度计算（基于关键词列表）
func calculateKeywordSimilarity(keywords1, keywords2 []string) float64 {
	if len(keywords1) == 0 || len(keywords2) == 0 {
		return 0.0
	}

	// 计算关键词重叠度
	commonKeywords := 0
	for _, k1 := range keywords1 {
		for _, k2 := range keywords2 {
			if k1 == k2 || isKeywordSimilar(k1, k2) {
				commonKeywords++
				break
			}
		}
	}

	maxKeywords := len(keywords1)
	if len(keywords2) > maxKeywords {
		maxKeywords = len(keywords2)
	}

	return float64(commonKeywords) / float64(maxKeywords)
}

// 检查是否有特殊相似关键词
func hasSpecialSimilarKeywords(keywords1, keywords2 []string) bool {
	for _, k1 := range keywords1 {
		for _, k2 := range keywords2 {
			if isKeywordSimilar(k1, k2) {
				return true
			}
		}
	}
	return false
}

// 检查关键词是否相似
func isKeywordSimilar(k1, k2 string) bool {
	similarPairs := [][]string{
		{"生理盐水", "生理性盐水"},
		{"精华液", "精华"},
		{"疤痕贴", "瘢痕贴"},
		{"疤痕敷贴", "瘢痕敷贴"},
		{"坐便椅", "坐便器"},
		{"坐便椅", "坐便凳"},
		{"医用棉签", "棉棒"},
		{"创可贴", "创口贴"},
	}

	for _, pair := range similarPairs {
		if (k1 == pair[0] && k2 == pair[1]) || (k1 == pair[1] && k2 == pair[0]) {
			return true
		}
	}

	return false
}

// 最长公共子序列计算
func longestCommonSubsequence(s1, s2 []rune) int {
	m, n := len(s1), len(s2)
	if m == 0 || n == 0 {
		return 0
	}

	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if s1[i-1] == s2[j-1] {
				dp[i][j] = dp[i-1][j-1] + 1
			} else {
				dp[i][j] = max(dp[i-1][j], dp[i][j-1])
			}
		}
	}

	return dp[m][n]
}

// 检查是否是特殊相似对
func isSpecialSimilarPair(text1, text2 string) bool {
	pairs := [][]string{
		{"生理盐水", "生理性盐水"},
		{"精华液", "精华"},
		{"疤痕贴", "瘢痕贴"},
		{"坐便椅", "坐便器"},
		{"医用棉签", "棉棒"},
	}

	for _, pair := range pairs {
		if (text1 == pair[0] && text2 == pair[1]) || (text1 == pair[1] && text2 == pair[0]) {
			return true
		}
	}

	return false
}

// 辅助函数
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func (sm *Memory) PreloadFromDB() {
	fmt.Println("开始从数据库预加载共享记忆...")

	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if sm.fetcher == nil {
		fmt.Println("! 错误: 数据库获取器未设置")
		return
	}

	// 确保数据结构已初始化
	if sm.productMap == nil {
		sm.productMap = make(map[string]Product)
	}
	if sm.coreTagMap == nil {
		sm.coreTagMap = make(map[string][]string)
	}
	if sm.categoryMap == nil {
		sm.categoryMap = make(map[string][]string)
	}
	if sm.similarityDB == nil {
		sm.similarityDB = make(map[string]string)
	}

	products, err := sm.fetcher.GetAllProducts()
	if err != nil {
		fmt.Printf("预加载失败: %v\n", err)
		return
	}

	count := 0
	for _, p := range products {
		// 检查产品名称是否为空
		if p.GetProductName() == "" {
			fmt.Println("! 警告: 跳过产品名称为空的产品")
			continue
		}

		// 使用标准化名称作为key
		normalized := NormalizeProductName(p.GetProductName())

		// 直接存储产品，不再进行内部转换
		sm.productMap[normalized] = p

		// 更新核心标签映射
		if p.GetCoreTag() != "" {
			sm.coreTagMap[p.GetCoreTag()] = append(
				sm.coreTagMap[p.GetCoreTag()],
				normalized, // 使用标准化名称
			)
		}

		// 更新类目映射
		if len(p.GetCategory2()) > 0 {
			for _, cat := range p.GetCategory2() {
				sm.categoryMap[cat] = append(
					sm.categoryMap[cat],
					normalized, // 使用标准化名称
				)
			}
		}

		// 更新相似度数据库
		sm.similarityDB[normalized] = p.GetCoreTag()

		count++

		// 每加载1000个产品执行一次内存优化
		if count%1000 == 0 {
			sm.optimizeMemory()
		}
	}
	fmt.Printf("✓ 加载 %d 个商品到共享记忆\n", count)
}

func (sm *Memory) Clear() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.productMap = make(map[string]Product)
	sm.coreTagMap = make(map[string][]string)
	sm.categoryMap = make(map[string][]string)
	sm.similarityDB = make(map[string]string)

	fmt.Println("✓ 共享记忆已重置")
}

func (sm *Memory) GetRelevantSamples(productName string, maxSamples int) []Product {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	var scoredProducts []struct {
		product Product
		score   float64
	}

	cfg := config.GetConfig()
	normalizedTarget := NormalizeProductName(productName)

	for _, p := range sm.productMap {
		if p.GetCoreTag() == "" {
			continue
		}

		normalizedSample := NormalizeProductName(p.GetProductName())
		similarity := calculateSimilarity(normalizedTarget, normalizedSample, cfg)

		if similarity >= cfg.ApiOptimization.MinSimilarity {
			scoredProducts = append(scoredProducts, struct {
				product Product
				score   float64
			}{p, similarity})
		}
	}

	sort.Slice(scoredProducts, func(i, j int) bool {
		return scoredProducts[i].score > scoredProducts[j].score
	})

	var samples []Product
	for i := 0; i < len(scoredProducts) && i < maxSamples; i++ {
		samples = append(samples, scoredProducts[i].product)
	}

	return samples
}

// 增强的相似度计算
func calculateSimilarity(a, b string, cfg *config.Config) float64 {
	if a == b {
		return 1.0
	}

	// 1. 应用规格标准化
	if cfg != nil && cfg.ApiOptimization.SpecStandardization {
		a = removeSpecSuffix(a)
		b = removeSpecSuffix(b)
		if a == b {
			return 0.95 // 规格不同但产品相同
		}
	}

	// 2. 基于共同词汇的相似度
	aWords := strings.Fields(a)
	bWords := strings.Fields(b)
	common := 0
	for _, aw := range aWords {
		for _, bw := range bWords {
			if aw == bw {
				common++
				break
			}
		}
	}

	// 3. 计算基础相似度
	total := len(aWords) + len(bWords)
	if total == 0 {
		return 0
	}
	baseSimilarity := float64(2*common) / float64(total)

	// 4. 长度差异惩罚
	lenDiff := float64(len(a)-len(b)) / float64(len(a)+len(b))
	if lenDiff < 0 {
		lenDiff = -lenDiff
	}

	return baseSimilarity * (1 - 0.2*lenDiff)
}

// 更精确的规格移除
func removeSpecSuffix(s string) string {
	// 匹配数字+单位 (如100ml, 10片, 20g)
	re := regexp.MustCompile(`\s*\d+[\p{L}\p{N}]*\s*$`)
	return re.ReplaceAllString(s, "")
}

// 增强的标准化函数
func NormalizeProductName(name string) string {
	// 0. 转换为小写 (不区分大小写)
	name = strings.ToLower(name)

	// 1. 移除括号内容
	re := regexp.MustCompile(`\[.*?\]|\(.*?\)`)
	name = re.ReplaceAllString(name, "")

	// 2. 移除特殊符号但保留中文
	re = regexp.MustCompile(`[^\p{Han}\w\s]`)
	name = re.ReplaceAllString(name, " ")

	// 3. 标准化斜杠
	name = strings.ReplaceAll(name, "／", "/")
	name = strings.ReplaceAll(name, "\\", "/")

	// 4. 移除多余空格
	name = strings.Join(strings.Fields(name), " ")

	// 5. 移除常见停用词
	stopWords := []string{"的", "和", "与", "及", "或", "是", "在", "了", "有"}
	for _, word := range stopWords {
		name = strings.ReplaceAll(name, " "+word+" ", " ")
	}

	return strings.TrimSpace(name)
}

// ======= DBProductFetcher 接口和适配器 =======

// DBProductFetcher 接口
type DBProductFetcher interface {
	GetAllProducts() ([]Product, error)
}

// DBProductAdapter 定义
type DBProductAdapter struct {
	db.Product
}

func (a DBProductAdapter) GetProductName() string   { return a.ProductName }
func (a DBProductAdapter) GetCoreTag() string       { return a.CoreTag }
func (a DBProductAdapter) GetScene() string         { return a.Scene }
func (a DBProductAdapter) GetCrowd() string         { return a.Crowd }
func (a DBProductAdapter) GetBrand() string         { return a.Brand }
func (a DBProductAdapter) GetSpec() string          { return a.Spec }
func (a DBProductAdapter) GetCategory1() []string   { return a.Category1 }
func (a DBProductAdapter) GetCategory2() []string   { return a.Category2 }
func (a DBProductAdapter) GetCleanedName() string   { return a.CleanedName }
func (a DBProductAdapter) GetFeatureTags() []string { return a.FeatureTags }
