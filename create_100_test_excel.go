//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"

	"github.com/xuri/excelize/v2"
)

func main() {
	// 创建新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 设置表头
	f.SetCellValue("Sheet1", "A1", "商品名称")

	// 100个测试产品列表
	products := []string{
		// 口罩类产品 (10个)
		"稳健医疗一次性医用外科口罩50只装成人防护三层熔喷布医用口罩",
		"3M医用防护口罩N95级别KN95口罩独立包装20只装",
		"朝美医用外科口罩一次性三层防护口罩50只装蓝色",
		"振德医用外科口罩一次性防护口罩成人50只装白色",
		"海氏海诺医用外科口罩一次性防护三层熔喷布50只装",
		"袋鼠医生儿童医用外科口罩一次性防护小号30只装",
		"可孚医用外科口罩一次性防护成人50只装独立包装",
		"鱼跃医用外科口罩一次性防护三层熔喷布50只装蓝色",
		"欧普康医用外科口罩一次性防护成人50只装白色",
		"康祝医用外科口罩一次性防护三层熔喷布50只装",

		// 酒精湿巾类产品 (10个)
		"海氏海诺75%酒精湿巾杀菌消毒湿纸巾便携装10片*20包",
		"稳健医疗75%酒精湿巾杀菌消毒湿纸巾大包装80片装",
		"振德75%酒精湿巾杀菌消毒湿纸巾便携装20片*10包",
		"可孚75%酒精湿巾杀菌消毒湿纸巾家用装50片*4包",
		"朝美75%酒精湿巾杀菌消毒湿纸巾便携装15片*12包",
		"袋鼠医生75%酒精湿巾杀菌消毒湿纸巾便携装10片*30包",
		"鱼跃75%酒精湿巾杀菌消毒湿纸巾大包装100片装",
		"欧普康75%酒精湿巾杀菌消毒湿纸巾便携装20片*8包",
		"康祝75%酒精湿巾杀菌消毒湿纸巾家用装60片*3包",
		"德佑75%酒精湿巾杀菌消毒湿纸巾便携装25片*6包",

		// 血压计类产品 (10个)
		"鱼跃yuwell血压计家用上臂式全自动高精准电子血压测量仪YE666AR",
		"欧姆龙OMRON电子血压计家用上臂式全自动血压测量仪HEM-7136",
		"可孚电子血压计家用上臂式全自动血压测量仪语音播报",
		"九安电子血压计家用上臂式全自动血压测量仪KD-5031",
		"乐心电子血压计家用上臂式全自动血压测量仪智能蓝牙",
		"松下电子血压计家用上臂式全自动血压测量仪EW-BU03",
		"爱奥乐电子血压计家用上臂式全自动血压测量仪AES-U181",
		"康祝电子血压计家用上臂式全自动血压测量仪语音播报",
		"鱼跃电子血压计家用手腕式便携血压测量仪YE8900A",
		"欧姆龙电子血压计家用手腕式便携血压测量仪HEM-6161",

		// 血糖试纸类产品 (10个)
		"三诺安稳血糖试纸50片装+采血针50支血糖仪试纸条",
		"强生稳捷血糖试纸50片装+采血针50支血糖仪试纸条",
		"罗氏活力血糖试纸50片装+采血针50支血糖仪试纸条",
		"雅培辅理善血糖试纸50片装+采血针50支血糖仪试纸条",
		"拜安进血糖试纸50片装+采血针50支血糖仪试纸条",
		"三诺安准血糖试纸50片装+采血针50支血糖仪试纸条",
		"怡成血糖试纸50片装+采血针50支血糖仪试纸条",
		"艾科血糖试纸50片装+采血针50支血糖仪试纸条",
		"鱼跃血糖试纸50片装+采血针50支血糖仪试纸条",
		"可孚血糖试纸50片装+采血针50支血糖仪试纸条",

		// 体温计类产品 (10个)
		"鱼跃电子体温计家用医用精准测温仪MT101",
		"欧姆龙电子体温计家用医用精准测温仪MC-343F",
		"可孚电子体温计家用医用精准测温仪KF-HW-001",
		"九安电子体温计家用医用精准测温仪KD-2201",
		"海尔电子体温计家用医用精准测温仪HCS-1000",
		"康祝电子体温计家用医用精准测温仪语音播报",
		"鱼跃红外线额温枪家用医用非接触式体温计YHW-2",
		"欧姆龙红外线额温枪家用医用非接触式体温计MC-720",
		"可孚红外线额温枪家用医用非接触式体温计KF-HW-004",
		"九安红外线额温枪家用医用非接触式体温计KD-2633",

		// 创可贴类产品 (10个)
		"邦迪创可贴防水透气创口贴100片装混合装",
		"云南白药创可贴止血创口贴100片装混合装",
		"海氏海诺创可贴防水透气创口贴100片装混合装",
		"稳健医疗创可贴防水透气创口贴100片装混合装",
		"振德创可贴防水透气创口贴100片装混合装",
		"可孚创可贴防水透气创口贴100片装混合装",
		"朝美创可贴防水透气创口贴100片装混合装",
		"袋鼠医生创可贴防水透气创口贴100片装混合装",
		"鱼跃创可贴防水透气创口贴100片装混合装",
		"欧普康创可贴防水透气创口贴100片装混合装",

		// 纱布类产品 (10个)
		"稳健医疗医用纱布块灭菌型10cm*10cm*8层100片装",
		"振德医用纱布块灭菌型10cm*10cm*8层100片装",
		"海氏海诺医用纱布块灭菌型10cm*10cm*8层100片装",
		"可孚医用纱布块灭菌型10cm*10cm*8层100片装",
		"朝美医用纱布块灭菌型10cm*10cm*8层100片装",
		"袋鼠医生医用纱布块灭菌型10cm*10cm*8层100片装",
		"鱼跃医用纱布块灭菌型10cm*10cm*8层100片装",
		"欧普康医用纱布块灭菌型10cm*10cm*8层100片装",
		"康祝医用纱布块灭菌型10cm*10cm*8层100片装",
		"德佑医用纱布块灭菌型10cm*10cm*8层100片装",

		// 棉签类产品 (10个)
		"稳健医疗医用棉签无菌棉棒100支装双头棉签",
		"振德医用棉签无菌棉棒100支装双头棉签",
		"海氏海诺医用棉签无菌棉棒100支装双头棉签",
		"可孚医用棉签无菌棉棒100支装双头棉签",
		"朝美医用棉签无菌棉棒100支装双头棉签",
		"袋鼠医生医用棉签无菌棉棒100支装双头棉签",
		"鱼跃医用棉签无菌棉棒100支装双头棉签",
		"欧普康医用棉签无菌棉棒100支装双头棉签",
		"康祝医用棉签无菌棉棒100支装双头棉签",
		"德佑医用棉签无菌棉棒100支装双头棉签",

		// 碘伏类产品 (10个)
		"海氏海诺碘伏消毒液皮肤伤口消毒500ml装",
		"稳健医疗碘伏消毒液皮肤伤口消毒500ml装",
		"振德碘伏消毒液皮肤伤口消毒500ml装",
		"可孚碘伏消毒液皮肤伤口消毒500ml装",
		"朝美碘伏消毒液皮肤伤口消毒500ml装",
		"袋鼠医生碘伏消毒液皮肤伤口消毒500ml装",
		"鱼跃碘伏消毒液皮肤伤口消毒500ml装",
		"欧普康碘伏消毒液皮肤伤口消毒500ml装",
		"康祝碘伏消毒液皮肤伤口消毒500ml装",
		"德佑碘伏消毒液皮肤伤口消毒500ml装",

		// 洗手液类产品 (10个)
		"滴露免洗洗手液杀菌消毒便携装50ml*6瓶",
		"威露士免洗洗手液杀菌消毒便携装50ml*6瓶",
		"海氏海诺免洗洗手液杀菌消毒便携装50ml*6瓶",
		"稳健医疗免洗洗手液杀菌消毒便携装50ml*6瓶",
		"振德免洗洗手液杀菌消毒便携装50ml*6瓶",
		"可孚免洗洗手液杀菌消毒便携装50ml*6瓶",
		"朝美免洗洗手液杀菌消毒便携装50ml*6瓶",
		"袋鼠医生免洗洗手液杀菌消毒便携装50ml*6瓶",
		"鱼跃免洗洗手液杀菌消毒便携装50ml*6瓶",
		"欧普康免洗洗手液杀菌消毒便携装50ml*6瓶",
	}

	// 写入产品数据
	for i, product := range products {
		cell := fmt.Sprintf("A%d", i+2)
		f.SetCellValue("Sheet1", cell, product)
	}

	// 保存文件
	if err := f.SaveAs("test_100_products.xlsx"); err != nil {
		log.Fatal(err)
	}

	fmt.Printf("✓ 成功创建100个产品的测试文件: test_100_products.xlsx\n")
}
