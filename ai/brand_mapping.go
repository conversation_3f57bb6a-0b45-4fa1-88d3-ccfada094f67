package ai

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/mogan/tag-assistant/config"
)

// BrandMappingConfig 品牌映射配置
type BrandMappingConfig struct {
	Mappings   map[string]string `json:"mappings"`
	NoiseWords []string          `json:"noise_words"`
}

var (
	brandMapping   BrandMappingConfig
	brandMapLoaded bool
	brandMutex     sync.RWMutex
	loadOnce       sync.Once
	loadError      error
)

// 加载品牌映射配置
func LoadBrandMapping() error {
	loadOnce.Do(func() {
		// 增加重试机制，提高并发环境下的成功率
		maxRetries := 3
		for i := range maxRetries {
			loadError = loadBrandMappingInternal()
			if loadError == nil {
				break
			}
			fmt.Printf("品牌映射加载失败(尝试 %d/%d): %v\n", i+1, maxRetries, loadError)
			if i < maxRetries-1 {
				time.Sleep(100 * time.Millisecond) // 短暂等待后重试
			}
		}
	})
	return loadError
}

// 内部加载函数
func loadBrandMappingInternal() error {

	cfg := config.GetConfig()
	if cfg == nil {
		return fmt.Errorf("配置未加载，无法获取品牌映射路径")
	}

	// 确定品牌映射文件路径
	brandMappingPath := ""
	if cfg.BrandMappingFile != "" {
		brandMappingPath = cfg.BrandMappingFile
	} else {
		// 默认路径：与配置文件同目录下的 brand_mapping.json
		configDir := filepath.Dir(config.ConfigPath)
		brandMappingPath = filepath.Join(configDir, "brand_mapping.json")
	}

	// 读取文件
	data, err := os.ReadFile(brandMappingPath)
	if err != nil {
		return fmt.Errorf("读取品牌映射文件失败: %w", err)
	}

	// 解析JSON
	var mapping BrandMappingConfig
	if err := json.Unmarshal(data, &mapping); err != nil {
		return fmt.Errorf("解析品牌映射文件失败: %w", err)
	}

	// 转换为小写键名，方便查找
	normalizedMappings := make(map[string]string)
	for k, v := range mapping.Mappings {
		normalizedMappings[strings.ToLower(k)] = v
	}
	mapping.Mappings = normalizedMappings

	brandMapping = mapping
	brandMapLoaded = true
	return nil
}

// 获取品牌映射配置
func GetBrandMapping() BrandMappingConfig {
	brandMutex.RLock()
	defer brandMutex.RUnlock()
	return brandMapping
}

// 标准化品牌名
func NormalizeBrand(brand string) string {
	// 如果品牌为空或无效，直接返回"无品牌"
	if brand == "" || brand == "无品牌" || brand == "未知品牌" || brand == "无" {
		return "无品牌"
	}

	// 确保品牌映射已加载，如果加载失败则保留原始品牌（不进行映射）
	if err := LoadBrandMapping(); err != nil {
		fmt.Printf("品牌映射加载失败: %v，保留原始品牌: %s\n", err, brand)
		// 对原始品牌进行基本的首字母大写处理
		if len(brand) > 0 {
			firstChar := brand[0]
			if firstChar >= 'a' && firstChar <= 'z' {
				return strings.ToUpper(string(firstChar)) + brand[1:]
			}
		}
		return brand
	}

	brandMutex.RLock()
	defer brandMutex.RUnlock()

	// 1. 移除噪声词
	cleanBrand := brand
	for _, noise := range brandMapping.NoiseWords {
		cleanBrand = strings.ReplaceAll(cleanBrand, noise, "")
	}

	lowerClean := strings.ToLower(cleanBrand)

	// 2. 精确匹配优先
	if mapped, exists := brandMapping.Mappings[lowerClean]; exists {
		return mapped
	}

	// 3. 新增：部分匹配（最长优先）
	var bestMatch string
	maxLength := 0
	for key, value := range brandMapping.Mappings {
		// 检查清理后的品牌是否包含映射键
		if strings.Contains(lowerClean, key) {
			// 找到最长匹配
			if len(key) > maxLength {
				bestMatch = value
				maxLength = len(key)
			}
		}
	}

	if bestMatch != "" {
		fmt.Printf("部分匹配成功: '%s' → '%s' (匹配键: %d字符)\n",
			brand, bestMatch, maxLength)
		return bestMatch
	}

	// 4. 无匹配时的处理（保持不变）
	result := brand
	if len(brand) > 0 {
		// 只对英文首字母大写，中文保持不变
		firstChar := brand[0]
		if firstChar >= 'a' && firstChar <= 'z' {
			// 英文首字母大写
			result = strings.ToUpper(string(firstChar)) + brand[1:]
		} else if firstChar >= 'A' && firstChar <= 'Z' {
			// 已经是英文大写，保持不变
		} else {
			// 中文或其他字符，保持不变
		}
	}

	return result
}

// 获取品牌噪声词
func GetBrandNoiseWords() []string {
	// 确保品牌映射已加载
	if err := LoadBrandMapping(); err != nil {
		fmt.Printf("品牌映射加载失败: %v\n", err)
		return []string{"专业版", "升级版", "Pro", "Max"} // 默认值
	}

	brandMutex.RLock()
	defer brandMutex.RUnlock()
	return brandMapping.NoiseWords
}

// AutoAddBrandMapping 自动添加品牌映射到文件
func AutoAddBrandMapping(productName, brandName string) error {
	fmt.Printf("[AutoAddBrandMapping] 🔄 开始自动添加品牌映射\n")
	fmt.Printf("[AutoAddBrandMapping] 产品名称: '%s'\n", productName)
	fmt.Printf("[AutoAddBrandMapping] 品牌名称: '%s'\n", brandName)

	// 确保品牌映射已加载
	if err := LoadBrandMapping(); err != nil {
		return fmt.Errorf("品牌映射加载失败: %w", err)
	}

	// 从产品名称中提取品牌关键词
	brandKey := extractBrandKeyFromProduct(productName, brandName)
	if brandKey == "" {
		fmt.Printf("[AutoAddBrandMapping] ⚠️ 无法提取有效的品牌关键词\n")
		return fmt.Errorf("无法提取有效的品牌关键词")
	}

	fmt.Printf("[AutoAddBrandMapping] 提取的品牌关键词: '%s'\n", brandKey)

	// 检查是否已存在
	brandMutex.RLock()
	lowerKey := strings.ToLower(brandKey)
	if existingBrand, exists := brandMapping.Mappings[lowerKey]; exists {
		brandMutex.RUnlock()
		fmt.Printf("[AutoAddBrandMapping] ℹ️ 品牌映射已存在: '%s' → '%s'\n", brandKey, existingBrand)
		return nil
	}
	brandMutex.RUnlock()

	// 添加新的品牌映射
	brandMutex.Lock()
	brandMapping.Mappings[lowerKey] = brandName
	brandMutex.Unlock()

	// 保存到文件
	if err := saveBrandMappingToFile(); err != nil {
		return fmt.Errorf("保存品牌映射文件失败: %w", err)
	}

	fmt.Printf("[AutoAddBrandMapping] ✅ 品牌映射添加成功: '%s' → '%s'\n", brandKey, brandName)
	return nil
}

// extractBrandKeyFromProduct 从产品名称中提取品牌关键词
func extractBrandKeyFromProduct(productName, brandName string) string {
	// 策略1：尝试从各种括号格式中提取
	bracketPatterns := []string{
		`\[([^\]]+)\]`, // [品牌名]
		`【([^】]+)】`,    // 【品牌名】
		`\{([^}]+)\}`,  // {品牌名}
		`「([^」]+)」`,    // 「品牌名」
		`〈([^〉]+)〉`,    // 〈品牌名〉
		`《([^》]+)》`,    // 《品牌名》
		`『([^』]+)』`,    // 『品牌名』
	}

	for _, pattern := range bracketPatterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(productName); len(matches) >= 2 {
			extracted := strings.TrimSpace(matches[1])
			if extracted != "" {
				fmt.Printf("[extractBrandKeyFromProduct] 从括号提取: '%s'\n", extracted)
				return extracted
			}
		}
	}

	// 策略2：如果品牌名在产品名开头，直接使用品牌名
	if strings.HasPrefix(strings.ToLower(productName), strings.ToLower(brandName)) {
		fmt.Printf("[extractBrandKeyFromProduct] 从开头匹配: '%s'\n", brandName)
		return brandName
	}

	// 策略3：查找品牌名在产品名中的位置
	lowerProduct := strings.ToLower(productName)
	lowerBrand := strings.ToLower(brandName)
	if strings.Contains(lowerProduct, lowerBrand) {
		fmt.Printf("[extractBrandKeyFromProduct] 从包含关系提取: '%s'\n", brandName)
		return brandName
	}

	// 策略4：尝试提取产品名开头的词汇作为品牌关键词
	words := strings.Fields(productName)
	if len(words) > 0 {
		firstWord := words[0]
		// 排除常见的医疗前缀
		medicalPrefixes := []string{"医用", "无菌", "一次性", "专业", "正品"}
		for _, prefix := range medicalPrefixes {
			if firstWord == prefix {
				if len(words) > 1 {
					firstWord = words[1]
				}
				break
			}
		}

		// 如果第一个词看起来像品牌名（包含字母或长度合适）
		if len(firstWord) >= 2 && len(firstWord) <= 15 {
			fmt.Printf("[extractBrandKeyFromProduct] 从首词提取: '%s'\n", firstWord)
			return firstWord
		}
	}

	fmt.Printf("[extractBrandKeyFromProduct] ❌ 无法提取品牌关键词\n")
	return ""
}

// saveBrandMappingToFile 保存品牌映射到文件
func saveBrandMappingToFile() error {
	cfg := config.GetConfig()
	if cfg == nil {
		return fmt.Errorf("配置未加载")
	}

	// 确定品牌映射文件路径
	brandMappingPath := ""
	if cfg.BrandMappingFile != "" {
		brandMappingPath = cfg.BrandMappingFile
	} else {
		// 默认路径：与配置文件同目录下的 brand_mapping.json
		configDir := filepath.Dir(config.ConfigPath)
		brandMappingPath = filepath.Join(configDir, "brand_mapping.json")
	}

	// 创建备份
	backupPath := brandMappingPath + ".backup." + time.Now().Format("20060102_150405")
	if _, err := os.Stat(brandMappingPath); err == nil {
		if err := copyFile(brandMappingPath, backupPath); err != nil {
			fmt.Printf("⚠️ 创建备份失败: %v\n", err)
		} else {
			fmt.Printf("📋 已创建备份: %s\n", backupPath)
		}
	}

	// 序列化为JSON
	data, err := json.MarshalIndent(brandMapping, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化品牌映射失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(brandMappingPath, data, 0644); err != nil {
		return fmt.Errorf("写入品牌映射文件失败: %w", err)
	}

	fmt.Printf("💾 品牌映射文件已更新: %s\n", brandMappingPath)
	return nil
}

// copyFile 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}
