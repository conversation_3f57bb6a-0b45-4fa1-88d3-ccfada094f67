package ai

import (
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/mogan/tag-assistant/config"
	"github.com/mogan/tag-assistant/db"
	"github.com/mogan/tag-assistant/db/mapping"
)

// 修改规则引擎初始化为延迟加载
var ruleEngineInstance *ruleEngine
var ruleEngineOnce sync.Once

func RuleEngine() *ruleEngine {
	ruleEngineOnce.Do(func() {
		ruleEngineInstance = newRuleEngine()
	})
	return ruleEngineInstance
}

type ClassificationRule struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Condition   string   `json:"condition"`
	Action      string   `json:"action"`
	Priority    int      `json:"priority"`
	Category2   []string `json:"category2"`
	CoreTag     string   `json:"core_tag"`
}

type ruleEngine struct {
	rules []ClassificationRule
	mutex sync.RWMutex
}

const (
	systemRulePrefix = "系统-" // 系统规则标识前缀
)

func newRuleEngine() *ruleEngine {
	engine := &ruleEngine{}
	// 立即加载规则
	err := engine.loadRules()
	if err != nil {
		fmt.Printf("[规则引擎] 初始化时加载规则失败: %v\n", err)
	}
	go engine.watchRuleChanges()
	return engine
}

// 添加初始化方法
func (re *ruleEngine) Init() {
	re.loadRules()
	go re.watchRuleChanges()
}

func (re *ruleEngine) ReloadRules() error {
	re.mutex.Lock()
	defer re.mutex.Unlock()

	fmt.Println("正在重新加载规则引擎...")

	if err := re.loadRules(); err != nil {
		return fmt.Errorf("加载规则失败: %w", err)
	}

	rules := re.rules
	fmt.Println("✓ 规则重新加载完成")
	fmt.Printf("- 系统规则: %d 条\n", countRulesByType(rules, true))
	fmt.Printf("- 用户规则: %d 条\n", countRulesByType(rules, false))
	fmt.Printf("- 总规则数: %d 条\n", len(rules))

	return nil
}

func (re *ruleEngine) GetAllRules() []ClassificationRule {
	re.mutex.RLock()
	defer re.mutex.RUnlock()
	return re.rules
}

func (re *ruleEngine) loadRules() error {
	cfg := config.GetConfig()

	if cfg == nil {
		fmt.Println("!!! 严重错误: 配置对象为 nil")
		re.rules = getBuiltinRules()
		re.sortRules()
		fmt.Println("✓ 使用内置规则作为后备")
		return fmt.Errorf("配置未加载")
	}

	if cfg.RuleEngine.RuleFile == "" {
		fmt.Println("! 警告: 规则引擎配置文件路径未配置")
		re.rules = getBuiltinRules()
		re.sortRules()
		fmt.Println("✓ 使用内置规则")
		return fmt.Errorf("规则文件路径未配置")
	}

	// 加载系统规则
	systemRules, err := loadRulesFromFile(cfg.RuleEngine.RuleFile)
	if err != nil {
		fmt.Printf("加载系统规则失败: %v\n", err)
		fmt.Println("! 使用内置规则作为后备")
		systemRules = getBuiltinRules()
	}

	// 为系统规则添加标识前缀
	for i := range systemRules {
		systemRules[i].Name = systemRulePrefix + systemRules[i].Name
	}

	// 加载用户规则
	userRules, err := loadRulesFromFile(cfg.RuleEngine.UserRuleFile)
	if err != nil {
		// 文件不存在时创建空文件
		if os.IsNotExist(err) {
			if err := createEmptyUserRulesFile(cfg.RuleEngine.UserRuleFile); err == nil {
				userRules = []ClassificationRule{}
				fmt.Println("✓ 创建空用户规则文件")
			} else {
				fmt.Printf("! 创建用户规则文件失败: %v\n", err)
				return fmt.Errorf("创建用户规则文件失败: %w", err)
			}
		} else {
			fmt.Printf("加载用户规则失败: %v\n", err)
			return fmt.Errorf("加载用户规则失败: %w", err)
		}
	}

	// 合并规则 (系统规则 + 用户规则)
	allRules := append(systemRules, userRules...)
	re.rules = allRules
	re.sortRules()

	// 简化日志输出
	fmt.Printf("规则引擎加载完成，规则数: %d\n", len(re.rules))
	return nil
}

// 辅助函数：按类型统计规则数量
func countRulesByType(rules []ClassificationRule, isSystem bool) int {
	count := 0
	for _, rule := range rules {
		if strings.HasPrefix(rule.Name, systemRulePrefix) == isSystem {
			count++
		}
	}
	return count
}

// 辅助函数：从文件加载规则
func loadRulesFromFile(filename string) ([]ClassificationRule, error) {
	if filename == "" {
		return []ClassificationRule{}, nil
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("打开规则文件失败: %w", err)
	}
	defer file.Close()

	var rules []ClassificationRule
	if err := json.NewDecoder(file).Decode(&rules); err != nil {
		return nil, fmt.Errorf("解析规则文件失败: %w", err)
	}
	return rules, nil
}

// 辅助函数：创建空用户规则文件
func createEmptyUserRulesFile(path string) error {
	file, err := os.Create(path)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	_, err = file.WriteString("[]")
	if err != nil {
		return fmt.Errorf("写入初始内容失败: %w", err)
	}
	return nil
}

func (re *ruleEngine) sortRules() {
	sort.Slice(re.rules, func(i, j int) bool {
		return re.rules[i].Priority > re.rules[j].Priority
	})
}

func (re *ruleEngine) ApplyRules(p *db.Product) error {
	re.mutex.RLock()
	defer re.mutex.RUnlock()

	fmt.Printf("[规则引擎] 开始处理商品: %s\n", p.ProductName)

	// 按动作类型和优先级分组规则
	var setRules []ClassificationRule
	var addRules []ClassificationRule
	var filterRules []ClassificationRule

	for _, rule := range re.rules {
		switch rule.Action {
		case "SET":
			setRules = append(setRules, rule)
		case "ADD":
			addRules = append(addRules, rule)
		case "FILTER":
			filterRules = append(filterRules, rule)
		}
	}

	// 按优先级排序所有规则
	sort.Slice(setRules, func(i, j int) bool {
		return setRules[i].Priority > setRules[j].Priority
	})
	sort.Slice(addRules, func(i, j int) bool {
		return addRules[i].Priority > addRules[j].Priority
	})
	sort.Slice(filterRules, func(i, j int) bool {
		return filterRules[i].Priority > filterRules[j].Priority
	})

	appliedRules := []string{}

	// 打印所有规则加载情况
	fmt.Printf("[规则引擎] 总共加载了 %d 个规则\n", len(re.rules))
	for i, rule := range re.rules {
		fmt.Printf("[规则引擎] 规则%d: %s (动作: %s, 优先级: %d)\n", i+1, rule.Name, rule.Action, rule.Priority)
	}

	// 打印所有SET规则
	fmt.Printf("[规则引擎] 总共有 %d 个SET规则\n", len(setRules))
	for i, rule := range setRules {
		fmt.Printf("[规则引擎] SET规则%d: %s (优先级: %d)\n", i+1, rule.Name, rule.Priority)
	}

	// 第一阶段：应用SET规则（只应用第一个匹配的高优先级规则）
	for _, rule := range setRules {
		fmt.Printf("[规则引擎] 检查SET规则: %s, 条件: %s\n", rule.Name, rule.Condition)
		fmt.Printf("[规则引擎] 当前商品: 名称=%s, 核心标签=%s\n", p.ProductName, p.CoreTag)

		if evaluateCondition(p, rule.Condition) {
			fmt.Printf("[规则引擎] 应用SET规则: %s (优先级: %d)\n", rule.Name, rule.Priority)
			re.applyAction(rule, p)
			appliedRules = append(appliedRules, rule.Name)
			break // SET规则只应用第一个匹配的
		} else {
			fmt.Printf("[规则引擎] SET规则不匹配: %s\n", rule.Name)
		}
	}

	// 第二阶段：应用ADD规则（可以应用多个）
	for _, rule := range addRules {
		if evaluateCondition(p, rule.Condition) {
			fmt.Printf("[规则引擎] 应用ADD规则: %s (优先级: %d)\n", rule.Name, rule.Priority)
			re.applyAction(rule, p)
			appliedRules = append(appliedRules, rule.Name)
		}
	}

	// 第三阶段：应用FILTER规则（可以应用多个）
	for _, rule := range filterRules {
		if evaluateCondition(p, rule.Condition) {
			fmt.Printf("[规则引擎] 应用FILTER规则: %s (优先级: %d)\n", rule.Name, rule.Priority)
			re.applyAction(rule, p)
			appliedRules = append(appliedRules, rule.Name)
		}
	}

	// 第四阶段：特殊人群强制类目检查
	if p.Crowd != "" {
		expected := getExpectedCategories(p.Crowd)
		if len(expected) > 0 {
			for _, exp := range expected {
				if !containsStringRule(p.Category2, exp) {
					fmt.Printf("[规则引擎] 自动添加人群类目: %s -> %s\n", p.Crowd, exp)
					p.Category2 = append(p.Category2, exp)
				}
			}
		}
	}

	// 第五阶段：类目过滤和映射
	originalCategories := make([]string, len(p.Category2))
	copy(originalCategories, p.Category2)

	p.Category2 = mapping.FilterCategories(p.Crowd, p.Category2)

	// 检查是否有类目被过滤
	if len(originalCategories) != len(p.Category2) {
		fmt.Printf("[规则引擎] 类目过滤: %v -> %v\n", originalCategories, p.Category2)
	}

	// 确保一级类目正确映射
	if len(p.Category2) > 0 && len(p.Category1) == 0 {
		p.Category1 = mapping.GetCategories1(p.Category2)
	}

	// 输出最终结果
	fmt.Printf("[规则引擎] 处理完成 - 应用规则: %v\n", appliedRules)
	fmt.Printf("[规则引擎] 最终类目: 一级=%v, 二级=%v\n", p.Category1, p.Category2)

	if len(appliedRules) > 0 {
		return nil
	}
	return fmt.Errorf("未匹配到适用规则")
}

// 评估条件表达式
func evaluateCondition(p *db.Product, expr string) bool {
	if expr == "" {
		return false
	}

	// 使用新的条件解析器
	condition, err := ParseCondition(expr)
	if err != nil {
		fmt.Printf("[规则引擎] 条件解析失败: %s, 错误: %v\n", expr, err)
		return false
	}

	result := condition.MatchesProduct(*p)
	fmt.Printf("[规则引擎] 条件评估: %s -> %t\n", expr, result)
	return result
}

func (re *ruleEngine) applyAction(rule ClassificationRule, p *db.Product) {
	// 设置核心标签
	if rule.CoreTag != "" {
		p.CoreTag = rule.CoreTag
	}

	switch rule.Action {
	case "SET":
		p.Category2 = mapping.FilterCategories(p.Crowd, rule.Category2)
		if len(p.Category2) > 0 {
			var category1 []string
			for _, cat2 := range p.Category2 {
				if cat1 := mapping.GetCategory1(cat2); cat1 != "" {
					category1 = append(category1, cat1)
				}
			}
			p.Category1 = category1
		}

	case "ADD":
		filteredCats := mapping.FilterCategories(p.Crowd, rule.Category2)
		newCats := []string{}

		for _, newCat := range filteredCats {
			exists := false
			for _, existing := range p.Category2 {
				if existing == newCat {
					exists = true
					break
				}
			}
			if !exists {
				newCats = append(newCats, newCat)
			}
		}

		p.Category2 = append(p.Category2, newCats...)
		if len(p.Category2) > 2 {
			p.Category2 = p.Category2[:2]
		}

		if len(p.Category2) > 0 {
			var category1 []string
			for _, cat2 := range p.Category2 {
				if cat1 := mapping.GetCategory1(cat2); cat1 != "" {
					category1 = append(category1, cat1)
				}
			}
			p.Category1 = category1
		}

	case "FILTER":
		var filtered []string
		for _, cat := range p.Category2 {
			shouldKeep := true
			for _, excluded := range rule.Category2 {
				if cat == excluded {
					shouldKeep = false
					break
				}
			}
			if shouldKeep {
				filtered = append(filtered, cat)
			}
		}
		p.Category2 = filtered

		if len(p.Category2) > 0 {
			var category1 []string
			for _, cat2 := range p.Category2 {
				if cat1 := mapping.GetCategory1(cat2); cat1 != "" {
					category1 = append(category1, cat1)
				}
			}
			p.Category1 = category1
		}
	}

	// 特殊人群强制类目检查
	if p.Crowd != "" {
		expected := getExpectedCategories(p.Crowd)
		found := false
		for _, exp := range expected {
			if containsStringRule(p.Category2, exp) {
				found = true
				break
			}
		}

		if !found {
			// 自动添加缺失的类目
			for _, cat := range expected {
				if !containsStringRule(p.Category2, cat) {
					p.Category2 = append(p.Category2, cat)
				}
			}
		}
	}

	// 确保类目映射正确（使用 Product 自身的方法）
	p.AutoMapCategories()

	// 确保类目数量不超过限制
	if len(p.Category2) > 2 {
		p.Category2 = p.Category2[:2]
	}

	// 确保一级类目正确
	if len(p.Category2) > 0 && len(p.Category1) == 0 {
		p.Category1 = mapping.GetCategories1(p.Category2)
	}

	// 规则应用后重新生成清洗名称
	db.GenerateCleanedName(p)
}

// 辅助函数：检查字符串是否在切片中
func containsStringRule(slice []string, str string) bool {
	for _, s := range slice {
		if s == str {
			return true
		}
	}
	return false
}

func (re *ruleEngine) watchRuleChanges() {
	cfg := config.GetConfig()
	if cfg == nil {
		fmt.Println("! 配置未加载，跳过规则监视")
		return
	}

	if !cfg.RuleEngine.EnableDynamicLoad {
		return
	}

	ticker := time.NewTicker(30 * time.Second)
	lastModTime := time.Time{}

	for range ticker.C {
		fileInfo, err := os.Stat(cfg.RuleEngine.UserRuleFile)
		if err != nil {
			continue
		}

		if fileInfo.ModTime().After(lastModTime) {
			fmt.Println("检测到用户规则文件变更，重新加载规则引擎...")
			if err := re.ReloadRules(); err != nil {
				fmt.Printf("! 规则重新加载失败: %v\n", err)
			}
			lastModTime = fileInfo.ModTime()
		}
	}
}

// 规则调试和监控功能
type RuleDebugInfo struct {
	RuleName      string
	Condition     string
	Action        string
	Priority      int
	Matched       bool
	Applied       bool
	ErrorMessage  string
	ExecutionTime time.Duration
}

type RuleExecutionReport struct {
	ProductName   string
	TotalRules    int
	MatchedRules  []RuleDebugInfo
	AppliedRules  []RuleDebugInfo
	FailedRules   []RuleDebugInfo
	FinalCategory []string
	TotalTime     time.Duration
}

// 生成规则执行报告
func (re *ruleEngine) GenerateExecutionReport(p *db.Product) RuleExecutionReport {
	startTime := time.Now()

	report := RuleExecutionReport{
		ProductName:  p.ProductName,
		TotalRules:   len(re.rules),
		MatchedRules: []RuleDebugInfo{},
		AppliedRules: []RuleDebugInfo{},
		FailedRules:  []RuleDebugInfo{},
	}

	for _, rule := range re.rules {
		ruleStartTime := time.Now()
		debugInfo := RuleDebugInfo{
			RuleName:  rule.Name,
			Condition: rule.Condition,
			Action:    rule.Action,
			Priority:  rule.Priority,
		}

		// 评估条件
		matched := evaluateCondition(p, rule.Condition)
		debugInfo.Matched = matched
		debugInfo.ExecutionTime = time.Since(ruleStartTime)

		if matched {
			report.MatchedRules = append(report.MatchedRules, debugInfo)
		} else {
			report.FailedRules = append(report.FailedRules, debugInfo)
		}
	}

	report.FinalCategory = p.Category2
	report.TotalTime = time.Since(startTime)

	return report
}

// 打印规则执行报告
func (report RuleExecutionReport) Print() {
	fmt.Printf("\n=== 规则执行报告 ===\n")
	fmt.Printf("商品名称: %s\n", report.ProductName)
	fmt.Printf("总规则数: %d\n", report.TotalRules)
	fmt.Printf("匹配规则数: %d\n", len(report.MatchedRules))
	fmt.Printf("失败规则数: %d\n", len(report.FailedRules))
	fmt.Printf("执行时间: %v\n", report.TotalTime)

	if len(report.MatchedRules) > 0 {
		fmt.Printf("\n匹配的规则:\n")
		for _, rule := range report.MatchedRules {
			fmt.Printf("  ✓ %s (优先级: %d, 动作: %s)\n", rule.RuleName, rule.Priority, rule.Action)
			fmt.Printf("    条件: %s\n", rule.Condition)
		}
	}

	if len(report.FailedRules) > 0 {
		fmt.Printf("\n未匹配的规则:\n")
		for _, rule := range report.FailedRules {
			fmt.Printf("  ✗ %s (优先级: %d)\n", rule.RuleName, rule.Priority)
		}
	}

	fmt.Printf("\n最终类目: %v\n", report.FinalCategory)
	fmt.Printf("==================\n")
}

func getExpectedCategories(crowd string) []string {
	mapping := map[string][]string{
		"孕妇": {"宝妈用品"},
		"产妇": {"宝妈用品"},
		"婴儿": {"婴儿用品"},
		"儿童": {"儿童保健"},
	}
	if cats, ok := mapping[crowd]; ok {
		return cats
	}
	return []string{}
}

// 在文件底部添加这个函数
func getBuiltinRules() []ClassificationRule {
	return []ClassificationRule{
		{
			Name:        "坐便类产品",
			Description: "坐便类产品直接映射到坐便辅助",
			Condition:   "contains(productName, '坐便')",
			Action:      "SET",
			Priority:    100,
			Category2:   []string{"坐便辅助"},
		},
		{
			Name:        "口罩类产品",
			Description: "口罩类产品直接映射到防护口罩",
			Condition:   "contains(productName, '口罩')",
			Action:      "SET",
			Priority:    90,
			Category2:   []string{"防护口罩"},
		},
		{
			Name:        "宝妈产品规则增强",
			Description: "宝妈/孕妇相关产品添加宝妈用品类目",
			Condition:   "contains(productName, '宝妈') OR contains(productName, '产妇') OR contains(productName, '孕妇') OR contains(productName, '月子') OR contains(productName, '待产') OR crowd = '孕妇' OR crowd = '产妇'",
			Action:      "ADD",
			Priority:    95,
			Category2:   []string{"宝妈用品"},
		},
		{
			Name:        "轮椅拐杖类产品",
			Description: "轮椅拐杖类产品映射到对应类目",
			Condition:   "contains(productName, '轮椅') OR contains(productName, '拐杖') OR contains(productName, '坐便辅助')",
			Action:      "SET",
			Priority:    80,
			Category2:   []string{"轮椅", "腋拐肘拐", "坐便辅助"},
		},
		{
			Name:        "营养补充产品",
			Description: "营养补充类产品映射到营养补充类目",
			Condition:   "contains(productName, '营养补充') OR contains(productName, '膳食补充')",
			Action:      "SET",
			Priority:    70,
			Category2:   []string{"营养补充"},
		},
		{
			Name:        "暖宝宝特殊处理",
			Description: "暖宝宝分类到热敷类目",
			Condition:   "contains(productName, '暖宝宝')",
			Action:      "SET",
			Priority:    95,
			Category2:   []string{"热敷/暖宫"},
		},
		{
			Name:        "孕妇禁用产品",
			Description: "孕妇禁用产品过滤掉宝妈相关分类",
			Condition:   "contains(productName, '孕妇禁用')",
			Action:      "FILTER",
			Priority:    90,
			Category2:   []string{"宝妈用品", "私处保养", "胸部护理"},
		},
		{
			Name:        "儿童禁用产品",
			Description: "儿童禁用产品过滤掉儿童相关分类",
			Condition:   "contains(productName, '儿童禁用') OR crowd = '儿童'",
			Action:      "FILTER",
			Priority:    90,
			Category2:   []string{"儿童保健", "婴儿用品", "儿童保健"},
		},
		{
			Name:        "婴儿禁用产品",
			Description: "婴儿禁用产品过滤掉婴儿相关分类",
			Condition:   "contains(productName, '婴儿禁用') OR crowd = '婴儿'",
			Action:      "FILTER",
			Priority:    90,
			Category2:   []string{"婴儿用品", "儿童保健"},
		},
		{
			Name:        "儿童人群专属类目",
			Description: "人群标签为儿童的商品添加儿童健康类目",
			Condition:   "crowd = '儿童'",
			Action:      "ADD",
			Priority:    85,
			Category2:   []string{"儿童保健"},
		},
		{
			Name:        "婴儿人群专属类目",
			Description: "人群标签为婴儿的商品添加婴儿用品类目",
			Condition:   "crowd = '婴儿'",
			Action:      "ADD",
			Priority:    85,
			Category2:   []string{"婴儿用品"},
		},
		{
			Name:        "孕妇人群强制分类",
			Description: "人群标签为孕妇的商品强制添加宝妈用品",
			Condition:   "crowd = '孕妇'",
			Action:      "ADD",
			Priority:    100,
			Category2:   []string{"宝妈用品"},
		},
		{
			Name:        "耳塞类产品",
			Description: "耳塞类产品统一使用'耳塞'核心标签，固定分到'睡眠改善'类目",
			Condition:   "contains(productName, '耳塞') OR coreTag = '隔音耳塞'",
			Action:      "SET",
			Priority:    100,
			Category2:   []string{"睡眠改善"},
			CoreTag:     "耳塞",
		},
	}
}
