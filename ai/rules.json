[{"name": "坐便类产品", "description": "坐便类产品直接映射到坐便辅助", "condition": "contains(productName, '坐便')", "action": "SET", "priority": 100, "category2": ["坐便辅助"]}, {"name": "口罩类产品", "description": "口罩类产品直接映射到防护口罩", "condition": "contains(productName, '口罩')", "action": "SET", "priority": 90, "category2": ["防护口罩"]}, {"name": "宝妈产品规则增强", "description": "宝妈/孕妇相关产品添加宝妈用品类目", "condition": "contains(productName, '宝妈') OR contains(productName, '产妇') OR contains(productName, '孕妇') OR contains(productName, '月子') OR contains(productName, '待产') OR crowd = '孕妇' OR crowd = '产妇'", "action": "ADD", "priority": 95, "category2": ["宝妈用品"]}, {"name": "轮椅拐杖类产品", "description": "轮椅拐杖类产品映射到对应类目", "condition": "contains(productName, '轮椅') OR contains(productName, '拐杖') OR contains(productName, '坐便辅助')", "action": "SET", "priority": 80, "category2": ["轮椅", "腋拐肘拐", "坐便辅助"]}, {"name": "营养补充产品", "description": "营养补充类产品映射到营养补充类目", "condition": "contains(productName, '营养补充') OR contains(productName, '膳食补充')", "action": "SET", "priority": 70, "category2": ["营养补充"]}, {"name": "暖宝宝特殊处理", "description": "暖宝宝分类到热敷类目", "condition": "contains(productName, '暖宝宝')", "action": "SET", "priority": 95, "category2": ["热敷/暖宫"]}, {"name": "孕妇禁用产品", "description": "孕妇禁用产品过滤掉宝妈相关分类", "condition": "contains(productName, '孕妇禁用')", "action": "FILTER", "priority": 90, "category2": ["宝妈用品", "私处保养", "胸部护理"]}, {"name": "儿童禁用产品", "description": "儿童禁用产品过滤掉儿童相关分类", "condition": "contains(productName, '儿童禁用') OR crowd = '儿童'", "action": "FILTER", "priority": 90, "category2": ["儿童保健", "婴儿用品", "儿童保健"]}, {"name": "婴儿禁用产品", "description": "婴儿禁用产品过滤掉婴儿相关分类", "condition": "contains(productName, '婴儿禁用') OR crowd = '婴儿'", "action": "FILTER", "priority": 90, "category2": ["婴儿用品", "儿童保健"]}, {"name": "儿童人群专属类目", "description": "人群标签为儿童的商品添加儿童健康类目", "condition": "crowd = '儿童'", "action": "ADD", "priority": 85, "category2": ["儿童保健"]}, {"name": "婴儿人群专属类目", "description": "人群标签为婴儿的商品添加婴儿用品类目", "condition": "crowd = '婴儿'", "action": "ADD", "priority": 85, "category2": ["婴儿用品"]}, {"name": "孕妇人群强制分类", "description": "人群标签为孕妇的商品强制添加宝妈用品", "condition": "crowd = '孕妇'", "action": "ADD", "priority": 100, "category2": ["宝妈用品"]}, {"name": "酒精湿巾核心标签修正", "description": "酒精湿巾商品核心标签后处理：如果商品名包含湿巾但核心标签只是酒精，则修正为酒精湿巾", "condition": "contains(productName, '湿巾') AND coreTag = '酒精'", "action": "SET", "priority": 200, "core_tag": "酒精湿巾"}, {"name": "酒精湿纸巾核心标签修正", "description": "酒精湿纸巾商品核心标签后处理：如果商品名包含湿纸巾但核心标签只是酒精，则修正为酒精湿巾", "condition": "contains(productName, '湿纸巾') AND coreTag = '酒精'", "action": "SET", "priority": 200, "core_tag": "酒精湿巾"}, {"name": "消毒湿巾核心标签修正", "description": "消毒湿巾商品核心标签后处理：如果商品名包含湿巾但核心标签只是消毒，则修正为消毒湿巾", "condition": "contains(productName, '湿巾') AND coreTag = '消毒'", "action": "SET", "priority": 200, "core_tag": "消毒湿巾"}, {"name": "消毒湿纸巾核心标签修正", "description": "消毒湿纸巾商品核心标签后处理：如果商品名包含湿纸巾但核心标签只是消毒，则修正为消毒湿巾", "condition": "contains(productName, '湿纸巾') AND coreTag = '消毒'", "action": "SET", "priority": 200, "core_tag": "消毒湿巾"}, {"name": "润唇膏类商品二级类目修正", "description": "润唇膏类商品二级类目修正：润唇膏属于化妆品，应归类为彩妆护理", "condition": "coreTag = '润唇膏' OR contains(coreTag, '润唇膏')", "action": "SET", "priority": 180, "category2": ["彩妆护理"]}, {"name": "错误二级类目修正", "description": "修正不存在的二级类目：肌肤保养应该修正为面部护理", "condition": "contains(category2, '肌肤保养')", "action": "SET", "priority": 180, "category2": ["面部护理"]}]