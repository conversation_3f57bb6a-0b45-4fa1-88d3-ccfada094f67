package ai

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/mogan/tag-assistant/db"
)

// Condition 表示解析后的条件
type Condition struct {
	Type     string      // "contains", "equals", "regex", "and", "or", "not"
	Field    string      // 字段名
	Value    string      // 值
	Left     *Condition  // 左子条件（用于逻辑操作）
	Right    *Condition  // 右子条件（用于逻辑操作）
	Child    *Condition  // 子条件（用于NOT操作）
	Children []Condition // 子条件列表（用于复杂逻辑）
	Negated  bool        // 是否为否定条件
}

// ToString 返回条件的字符串表示
func (c Condition) ToString() string {
	prefix := ""
	if c.Negated {
		prefix = "!"
	}

	switch c.Type {
	case "contains":
		return fmt.Sprintf("%scontains(%s, '%s')", prefix, c.Field, c.Value)
	case "equals":
		return fmt.Sprintf("%s%s = '%s'", prefix, c.Field, c.Value)
	case "regex":
		return fmt.Sprintf("%sregex(%s, '%s')", prefix, c.Field, c.Value)
	case "and":
		if c.Left != nil && c.Right != nil {
			return fmt.Sprintf("(%s AND %s)", c.Left.ToString(), c.Right.ToString())
		}
	case "or":
		if c.Left != nil && c.Right != nil {
			return fmt.Sprintf("(%s OR %s)", c.Left.ToString(), c.Right.ToString())
		}
	case "not":
		if c.Child != nil {
			return fmt.Sprintf("NOT(%s)", c.Child.ToString())
		}
	default:
		return "未知条件"
	}
	return "未知条件"
}

// ParseCondition 解析条件表达式，支持复杂逻辑
func ParseCondition(expr string) (Condition, error) {
	expr = strings.TrimSpace(expr)

	// 处理括号表达式
	if strings.HasPrefix(expr, "(") && strings.HasSuffix(expr, ")") {
		// 检查是否是完整的括号表达式
		if isCompleteParentheses(expr) {
			inner := expr[1 : len(expr)-1]
			return ParseCondition(inner)
		}
	}

	// 处理OR逻辑（优先级较低）
	if orIndex := findTopLevelOperator(expr, " OR "); orIndex != -1 {
		return parseLogicalExpression(expr, "OR")
	}

	// 处理AND逻辑（优先级较高）
	if andIndex := findTopLevelOperator(expr, " AND "); andIndex != -1 {
		return parseLogicalExpression(expr, "AND")
	}

	// 解析单个条件
	return parseSingleCondition(expr)
}

// parseLogicalExpression 解析逻辑表达式
func parseLogicalExpression(expr, operator string) (Condition, error) {
	parts := strings.Split(expr, " "+operator+" ")
	if len(parts) < 2 {
		return Condition{}, fmt.Errorf("逻辑表达式格式错误: %s", expr)
	}

	// 解析第一个条件
	left, err := ParseCondition(strings.TrimSpace(parts[0]))
	if err != nil {
		return Condition{}, fmt.Errorf("解析左条件失败: %w", err)
	}

	// 如果只有两个部分，创建二元逻辑条件
	if len(parts) == 2 {
		right, err := ParseCondition(strings.TrimSpace(parts[1]))
		if err != nil {
			return Condition{}, fmt.Errorf("解析右条件失败: %w", err)
		}

		return Condition{
			Type:  strings.ToLower(operator),
			Left:  &left,
			Right: &right,
		}, nil
	}

	// 多个条件的情况，递归处理
	remaining := strings.Join(parts[1:], " "+operator+" ")
	right, err := parseLogicalExpression(remaining, operator)
	if err != nil {
		return Condition{}, fmt.Errorf("解析右表达式失败: %w", err)
	}

	return Condition{
		Type:  strings.ToLower(operator),
		Left:  &left,
		Right: &right,
	}, nil
}

// parseSingleCondition 解析单个条件
func parseSingleCondition(expr string) (Condition, error) {
	expr = strings.TrimSpace(expr)

	// 检查是否为否定条件
	negated := false
	if strings.HasPrefix(expr, "!") {
		negated = true
		expr = strings.TrimSpace(expr[1:])
	}

	// 解析 contains 条件
	if strings.HasPrefix(expr, "contains(") && strings.HasSuffix(expr, ")") {
		inner := expr[9 : len(expr)-1]
		parts := strings.Split(inner, ",")
		if len(parts) != 2 {
			return Condition{}, fmt.Errorf("contains条件格式错误")
		}
		return Condition{
			Type:    "contains",
			Field:   strings.TrimSpace(parts[0]),
			Value:   strings.Trim(strings.TrimSpace(parts[1]), "'\""),
			Negated: negated,
		}, nil
	}

	// 解析 equals 条件
	if strings.Contains(expr, "=") {
		parts := strings.SplitN(expr, "=", 2)
		if len(parts) != 2 {
			return Condition{}, fmt.Errorf("等于条件格式错误")
		}
		return Condition{
			Type:    "equals",
			Field:   strings.TrimSpace(parts[0]),
			Value:   strings.Trim(strings.TrimSpace(parts[1]), "'\""),
			Negated: negated,
		}, nil
	}

	// 解析 regex 条件
	if strings.HasPrefix(expr, "regex(") && strings.HasSuffix(expr, ")") {
		inner := expr[6 : len(expr)-1]
		parts := strings.Split(inner, ",")
		if len(parts) != 2 {
			return Condition{}, fmt.Errorf("正则条件格式错误")
		}
		return Condition{
			Type:    "regex",
			Field:   strings.TrimSpace(parts[0]),
			Value:   strings.Trim(strings.TrimSpace(parts[1]), "'\""),
			Negated: negated,
		}, nil
	}

	return Condition{}, fmt.Errorf("无法识别的条件格式: %s", expr)
}

// isCompleteParentheses 检查表达式是否被完整的括号包围
func isCompleteParentheses(expr string) bool {
	if !strings.HasPrefix(expr, "(") || !strings.HasSuffix(expr, ")") {
		return false
	}

	level := 0
	for i, char := range expr {
		switch char {
		case '(':
			level++
		case ')':
			level--
			// 如果在最后一个字符之前level就变成0，说明括号不完整
			if level == 0 && i < len(expr)-1 {
				return false
			}
		}
	}
	return level == 0
}

// findTopLevelOperator 查找顶级逻辑操作符的位置（不在括号内的）
func findTopLevelOperator(expr, operator string) int {
	level := 0
	operatorLen := len(operator)

	for i := 0; i <= len(expr)-operatorLen; i++ {
		char := expr[i]
		if char == '(' {
			level++
		} else if char == ')' {
			level--
		} else if level == 0 && strings.HasPrefix(expr[i:], operator) {
			return i
		}
	}
	return -1
}

// MatchesProduct 检查商品是否匹配条件
func (c Condition) MatchesProduct(p db.Product) bool {
	switch c.Type {
	case "and":
		if c.Left != nil && c.Right != nil {
			return c.Left.MatchesProduct(p) && c.Right.MatchesProduct(p)
		}
		return false
	case "or":
		if c.Left != nil && c.Right != nil {
			return c.Left.MatchesProduct(p) || c.Right.MatchesProduct(p)
		}
		return false
	case "not":
		if c.Child != nil {
			return !c.Child.MatchesProduct(p)
		}
		return false
	case "contains", "equals", "regex":
		result := c.matchesSingleCondition(p)
		// 如果是否定条件，返回相反结果
		if c.Negated {
			return !result
		}
		return result
	default:
		return false
	}
}

// matchesSingleCondition 检查单个条件
func (c Condition) matchesSingleCondition(p db.Product) bool {
	var fieldValue string

	switch c.Field {
	case "productName":
		fieldValue = p.ProductName
	case "coreTag":
		fieldValue = p.CoreTag
	case "scene":
		fieldValue = p.Scene
	case "crowd":
		fieldValue = p.Crowd
	case "brand":
		fieldValue = p.Brand
	case "spec":
		fieldValue = p.Spec
	case "category2":
		// 对于category2，检查是否包含在数组中
		for _, cat := range p.Category2 {
			if c.matchesStringValue(cat) {
				return true
			}
		}
		return false
	default:
		return false
	}

	return c.matchesStringValue(fieldValue)
}

// matchesStringValue 检查字符串值是否匹配条件
func (c Condition) matchesStringValue(value string) bool {
	switch c.Type {
	case "contains":
		return strings.Contains(strings.ToLower(value), strings.ToLower(c.Value))
	case "equals":
		return strings.EqualFold(value, c.Value)
	case "regex":
		matched, _ := regexp.MatchString(c.Value, value)
		return matched
	default:
		return false
	}
}
